-- ESX Compatibility Helper for STG Clothing
-- کمک کننده سازگاری ESX برای STG Clothing

-- تابع کمکی برای گرفتن پول نقد
function GetPlayerCash(xPlayer)
    if not xPlayer then return 0 end
    
    if xPlayer.getMoney then
        return xPlayer.getMoney()
    elseif xPlayer.money then
        return xPlayer.money
    else
        return 0
    end
end

-- تابع کمکی برای گرفتن پول بانک
function GetPlayerBank(xPlayer)
    if not xPlayer then return 0 end
    
    -- روش جدید ESX
    if xPlayer.getAccount then
        local bankAccount = xPlayer.getAccount('bank')
        if bankAccount then
            return bankAccount.money or 0
        end
    end
    
    -- روش قدیمی ESX
    if xPlayer.accounts then
        for i=1, #xPlayer.accounts do
            if xPlayer.accounts[i].name == 'bank' then
                return xPlayer.accounts[i].money or 0
            end
        end
    end
    
    return 0
end

-- تابع کمکی برای کم کردن پول نقد
function RemovePlayerCash(xPlayer, amount)
    if not xPlayer or not amount or amount <= 0 then return false end
    
    local currentMoney = GetPlayerCash(xPlayer)
    if currentMoney < amount then return false end
    
    if xPlayer.removeMoney then
        local result = xPlayer.removeMoney(amount)
        return result ~= false -- برخی ESX ها nil برمیگردونن
    elseif xPlayer.money then
        xPlayer.money = xPlayer.money - amount
        return true
    end
    
    return false
end

-- تابع کمکی برای کم کردن پول بانک
function RemovePlayerBank(xPlayer, amount)
    if not xPlayer or not amount or amount <= 0 then return false end
    
    local currentMoney = GetPlayerBank(xPlayer)
    if currentMoney < amount then return false end
    
    -- روش جدید ESX
    if xPlayer.removeAccountMoney then
        local result = xPlayer.removeAccountMoney('bank', amount)
        return result ~= false -- برخی ESX ها nil برمیگردونن
    end
    
    -- روش قدیمی ESX
    if xPlayer.accounts then
        for i=1, #xPlayer.accounts do
            if xPlayer.accounts[i].name == 'bank' then
                if xPlayer.accounts[i].money >= amount then
                    xPlayer.accounts[i].money = xPlayer.accounts[i].money - amount
                    return true
                end
                break
            end
        end
    end
    
    return false
end

-- تابع کمکی برای اضافه کردن پول نقد
function AddPlayerCash(xPlayer, amount)
    if not xPlayer or not amount or amount <= 0 then return false end
    
    if xPlayer.addMoney then
        xPlayer.addMoney(amount)
        return true
    elseif xPlayer.money then
        xPlayer.money = xPlayer.money + amount
        return true
    end
    
    return false
end

-- تابع کمکی برای اضافه کردن پول بانک
function AddPlayerBank(xPlayer, amount)
    if not xPlayer or not amount or amount <= 0 then return false end
    
    -- روش جدید ESX
    if xPlayer.addAccountMoney then
        xPlayer.addAccountMoney('bank', amount)
        return true
    end
    
    -- روش قدیمی ESX
    if xPlayer.accounts then
        for i=1, #xPlayer.accounts do
            if xPlayer.accounts[i].name == 'bank' then
                xPlayer.accounts[i].money = xPlayer.accounts[i].money + amount
                return true
            end
        end
    end
    
    return false
end

-- دستور تست برای بررسی سازگاری
RegisterCommand('test_esx_compat', function(source, args, rawCommand)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then
        print("^1Player not found^7")
        return
    end
    
    local cash = GetPlayerCash(xPlayer)
    local bank = GetPlayerBank(xPlayer)
    
    print("^2[ESX Compat] Player Money:^7")
    print("^2  Cash: $" .. cash .. "^7")
    print("^2  Bank: $" .. bank .. "^7")
    
    -- تست کم کردن و اضافه کردن پول
    if RemovePlayerCash(xPlayer, 1) then
        print("^2[ESX Compat] ✓ Cash removal works^7")
        AddPlayerCash(xPlayer, 1)
        print("^2[ESX Compat] ✓ Cash addition works^7")
    else
        print("^1[ESX Compat] ✗ Cash removal failed^7")
    end
    
    if RemovePlayerBank(xPlayer, 1) then
        print("^2[ESX Compat] ✓ Bank removal works^7")
        AddPlayerBank(xPlayer, 1)
        print("^2[ESX Compat] ✓ Bank addition works^7")
    else
        print("^1[ESX Compat] ✗ Bank removal failed^7")
    end
end, false)

print("^2[ESX Compatibility] Helper functions loaded^7")
print("^2  Use /test_esx_compat to test compatibility^7")
