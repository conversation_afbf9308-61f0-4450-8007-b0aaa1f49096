-- Test script for users table integration
-- اسکریپت تست برای یکپارچگی با جدول users

RegisterCommand('test_users_skin', function(source, args, rawCommand)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then
        print("Player not found")
        return
    end
    
    -- تست گرفتن اسکین از جدول users
    ESX.TriggerServerCallback('stg_clothing:getSkin', function(data)
        if data and data.skin then
            print("✓ Skin data found in users table")
            TriggerClientEvent('chat:addMessage', source, {
                color = {0, 255, 0},
                args = {"STG Test", "Skin data found in users table"}
            })
        else
            print("! No skin data in users table")
            TriggerClientEvent('chat:addMessage', source, {
                color = {255, 255, 0},
                args = {"STG Test", "No skin data found - this is normal for new players"}
            })
        end
    end)
end, false)

print("^2[STG Test] Users table test command loaded: /test_users_skin^7")
