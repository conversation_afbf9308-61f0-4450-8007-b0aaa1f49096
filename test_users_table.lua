-- Test script for users table integration
-- اسکریپت تست برای یکپارچگی با جدول users

-- Server-side command
RegisterCommand('test_users_skin', function(source, args, rawCommand)
    if source == 0 then
        print("This command must be used in-game")
        return
    end

    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then
        print("Player not found")
        return
    end

    print("^3[STG Test] Testing users table for player: " .. xPlayer.identifier .. "^7")

    -- تست مستقیم دیتابیس
    MySQL.query('SELECT skin FROM users WHERE identifier = ?', {
        xPlayer.identifier
    }, function(result)
        if result and #result > 0 then
            if result[1].skin and result[1].skin ~= "" then
                print("^2[STG Test] ✓ Skin data found in users table^7")
                TriggerClientEvent('chat:addMessage', source, {
                    color = {0, 255, 0},
                    args = {"STG Test", "✓ Skin data found in users table"}
                })
            else
                print("^3[STG Test] ! Skin field is empty in users table^7")
                TriggerClientEvent('chat:addMessage', source, {
                    color = {255, 255, 0},
                    args = {"STG Test", "! Skin field is empty - normal for new players"}
                })
            end
        else
            print("^1[STG Test] ✗ User not found in users table^7")
            TriggerClientEvent('chat:addMessage', source, {
                color = {255, 0, 0},
                args = {"STG Test", "✗ User not found in users table"}
            })
        end
    end)
end, false)

-- دستور ساده برای چک کردن callback
RegisterCommand('test_callback', function(source, args, rawCommand)
    if source == 0 then
        print("This command must be used in-game")
        return
    end

    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then
        print("Player not found")
        return
    end

    print("^3[STG Test] Testing getSkin callback...^7")

    -- فراخوانی مستقیم callback function
    local callback = ESX.ServerCallbacks['stg_clothing:getSkin']
    if callback then
        callback(source, function(data)
            if data and data.skin then
                print("^2[STG Test] ✓ Callback returned skin data^7")
                TriggerClientEvent('chat:addMessage', source, {
                    color = {0, 255, 0},
                    args = {"STG Test", "✓ Callback works - skin data found"}
                })
            else
                print("^3[STG Test] ! Callback returned nil^7")
                TriggerClientEvent('chat:addMessage', source, {
                    color = {255, 255, 0},
                    args = {"STG Test", "! Callback returned nil - normal for new players"}
                })
            end
        end)
    else
        print("^1[STG Test] ✗ Callback not found^7")
    end
end, false)

-- دستور ساده برای چک کردن جدول users
RegisterCommand('check_users_table', function(source, args, rawCommand)
    if source == 0 then
        print("This command must be used in-game")
        return
    end

    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then
        print("Player not found")
        return
    end

    print("^3[STG Test] Checking users table structure...^7")

    -- چک کردن ساختار جدول
    MySQL.query('DESCRIBE users', {}, function(result)
        local hasSkinField = false
        if result then
            for i=1, #result do
                if result[i].Field == 'skin' then
                    hasSkinField = true
                    print("^2[STG Test] ✓ Skin field exists in users table^7")
                    break
                end
            end
        end

        if not hasSkinField then
            print("^1[STG Test] ✗ Skin field NOT found in users table^7")
            TriggerClientEvent('chat:addMessage', source, {
                color = {255, 0, 0},
                args = {"STG Test", "✗ Skin field missing! Run the SQL update first"}
            })
        else
            TriggerClientEvent('chat:addMessage', source, {
                color = {0, 255, 0},
                args = {"STG Test", "✓ Users table is ready"}
            })
        end
    end)
end, false)

-- دستور تست برای save کردن اسکین
RegisterCommand('test_save_skin', function(source, args, rawCommand)
    if source == 0 then
        print("This command must be used in-game")
        return
    end

    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then
        print("Player not found")
        return
    end

    print("^3[STG Test] Testing skin save...^7")

    -- ایجاد یک اسکین تست
    local testSkin = {
        mask = {item = 1, texture = 0},
        hat = {item = -1, texture = 0},
        accessory = {item = 0, texture = 0},
        glass = {item = -1, texture = 0},
        bag = {item = 0, texture = 0},
        ["t-shirt"] = {item = 15, texture = 0},
        watch = {item = -1, texture = 0},
        arms = {item = 0, texture = 0},
        pants = {item = 1, texture = 0},
        shoes = {item = 1, texture = 0},
        torso2 = {item = 0, texture = 0},
        vest = {item = 0, texture = 0},
        hair = {item = 1, texture = 0},
        beard = {item = 0, texture = 0},
        makeup = {item = 0, texture = 0},
        sex = 0
    }

    local skinJson = json.encode(testSkin)

    -- فراخوانی مستقیم saveSkin event
    TriggerEvent('stg_clothing:saveSkin', 1885233650, skinJson)

    TriggerClientEvent('chat:addMessage', source, {
        color = {0, 255, 255},
        args = {"STG Test", "Test skin save triggered - check console"}
    })
end, false)

-- دستور چک کردن ESX
RegisterCommand('check_esx', function(source, args, rawCommand)
    if source == 0 then
        print("This command must be used in-game")
        return
    end

    print("^3[STG Test] Checking ESX status...^7")
    print("^3[STG Test] ESX object: " .. tostring(ESX) .. "^7")

    if ESX then
        local xPlayer = ESX.GetPlayerFromId(source)
        print("^3[STG Test] Player object: " .. tostring(xPlayer) .. "^7")

        if xPlayer then
            print("^2[STG Test] ✓ ESX working - Player: " .. xPlayer.identifier .. "^7")
            TriggerClientEvent('chat:addMessage', source, {
                color = {0, 255, 0},
                args = {"STG Test", "✓ ESX working - " .. xPlayer.identifier}
            })
        else
            print("^1[STG Test] ✗ ESX found but GetPlayerFromId failed^7")
            TriggerClientEvent('chat:addMessage', source, {
                color = {255, 0, 0},
                args = {"STG Test", "✗ ESX GetPlayerFromId failed"}
            })
        end
    else
        print("^1[STG Test] ✗ ESX not found^7")
        TriggerClientEvent('chat:addMessage', source, {
            color = {255, 0, 0},
            args = {"STG Test", "✗ ESX not found"}
        })
    end
end, false)

-- دستور چک کردن identifiers
RegisterCommand('check_identifiers', function(source, args, rawCommand)
    if source == 0 then
        print("This command must be used in-game")
        return
    end

    print("^3[STG Test] Checking player identifiers for source: " .. source .. "^7")

    local identifiers = {}
    for i = 0, GetNumPlayerIdentifiers(source) - 1 do
        local identifier = GetPlayerIdentifier(source, i)
        table.insert(identifiers, identifier)
        print("^3[STG Test] Identifier " .. i .. ": " .. identifier .. "^7")
    end

    TriggerClientEvent('chat:addMessage', source, {
        color = {0, 255, 255},
        args = {"STG Test", "Found " .. #identifiers .. " identifiers - check console"}
    })
end, false)

print("^2[STG Test] Users table test commands loaded:^7")
print("^2  /test_users_skin - Direct database test^7")
print("^2  /test_callback - Callback test^7")
print("^2  /check_users_table - Check table structure^7")
print("^2  /test_save_skin - Test skin saving^7")
print("^2  /check_esx - Check ESX status^7")
print("^2  /check_identifiers - Check player identifiers^7")
