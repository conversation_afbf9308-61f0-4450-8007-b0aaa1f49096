local isCameraActive = false
local currentSkin = nil
local zoomOffset = 0.6
local camOffset = 0.65
local heading, money, angle, lastBag, sleep = 0, 0, 0, 0, 0
local lastClothe, isProp, isBarber = false, false, false
local clothingList = {}
local currentSkin = {}

ESX = nil

Citizen.CreateThread(function()
    while ESX == nil do
        TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)
        Citizen.Wait(0)
    end
end)

-- Load skin when player spawns
AddEventHandler('esx:playerLoaded', function(xPlayer)
    Citizen.Wait(2000) -- Wait for character to fully load
    LoadPlayerSkin()
end)

AddEventHandler('esx:onPlayerSpawn', function()
    Citizen.Wait(2000) -- Wait for character to fully load
    LoadPlayerSkin()
end)

-- Function to load player skin from database
function LoadPlayerSkin()
    print("^3[STG Clothing] Loading player skin...^7")

    ESX.TriggerServerCallback('stg_clothing:getSkin', function(data)
        if data and data["skin"] then
            local success, decodedSkin = pcall(json.decode, data["skin"])
            if success and decodedSkin then
                print("^2[STG Clothing] ✓ Skin loaded from database^7")
                ApplySkinToPlayer(decodedSkin)
            else
                print("^1[STG Clothing] ✗ Failed to decode skin data^7")
            end
        else
            print("^3[STG Clothing] No skin data found, using default^7")
        end
    end)
end

-- Function to apply skin to player
function ApplySkinToPlayer(skin)
    local playerPed = PlayerPedId()

    -- Apply clothing items
    if skin["mask"] and skin["mask"]["item"] then
        SetPedComponentVariation(playerPed, 1, skin["mask"]["item"], skin["mask"]["texture"] or 0, 0)
    end

    if skin["arms"] and skin["arms"]["item"] then
        SetPedComponentVariation(playerPed, 3, skin["arms"]["item"], skin["arms"]["texture"] or 0, 0)
    end

    if skin["pants"] and skin["pants"]["item"] then
        SetPedComponentVariation(playerPed, 4, skin["pants"]["item"], skin["pants"]["texture"] or 0, 0)
    end

    if skin["bag"] and skin["bag"]["item"] then
        SetPedComponentVariation(playerPed, 5, skin["bag"]["item"], skin["bag"]["texture"] or 0, 0)
    end

    if skin["shoes"] and skin["shoes"]["item"] then
        SetPedComponentVariation(playerPed, 6, skin["shoes"]["item"], skin["shoes"]["texture"] or 0, 0)
    end

    if skin["accessory"] and skin["accessory"]["item"] then
        SetPedComponentVariation(playerPed, 7, skin["accessory"]["item"], skin["accessory"]["texture"] or 0, 0)
    end

    if skin["t-shirt"] and skin["t-shirt"]["item"] then
        SetPedComponentVariation(playerPed, 8, skin["t-shirt"]["item"], skin["t-shirt"]["texture"] or 0, 0)
    end

    if skin["vest"] and skin["vest"]["item"] then
        SetPedComponentVariation(playerPed, 9, skin["vest"]["item"], skin["vest"]["texture"] or 0, 0)
    end

    if skin["torso2"] and skin["torso2"]["item"] then
        SetPedComponentVariation(playerPed, 11, skin["torso2"]["item"], skin["torso2"]["texture"] or 0, 0)
    end

    -- Apply props
    if skin["hat"] and skin["hat"]["item"] and skin["hat"]["item"] ~= -1 then
        SetPedPropIndex(playerPed, 0, skin["hat"]["item"], skin["hat"]["texture"] or 0, true)
    else
        ClearPedProp(playerPed, 0)
    end

    if skin["glass"] and skin["glass"]["item"] and skin["glass"]["item"] ~= -1 then
        SetPedPropIndex(playerPed, 1, skin["glass"]["item"], skin["glass"]["texture"] or 0, true)
    else
        ClearPedProp(playerPed, 1)
    end

    if skin["watch"] and skin["watch"]["item"] and skin["watch"]["item"] ~= -1 then
        SetPedPropIndex(playerPed, 6, skin["watch"]["item"], skin["watch"]["texture"] or 0, true)
    else
        ClearPedProp(playerPed, 6)
    end

    print("^2[STG Clothing] ✓ Skin applied to player^7")
end

Citizen.CreateThread(function()
    for k,v in pairs(STG.Shops) do
        local blipInfo = STG.blip[v.type]
        local blip = AddBlipForCoord(STG.Shops[k]["coord"])
        SetBlipSprite(blip, blipInfo.sprite)
        SetBlipDisplay(blip, 4)
        SetBlipScale(blip, blipInfo.scale)
        SetBlipColour(blip, blipInfo.color)
        SetBlipAsShortRange(blip, true)
        BeginTextCommandSetBlipName("STRING")
        AddTextComponentString(blipInfo.label)
        EndTextCommandSetBlipName(blip)
    end
end)

Citizen.CreateThread(function()
    while true do
        find = false
        for k,v in pairs(STG.Shops) do
            local shopCoords = v["coord"]
            local coords = vector3(shopCoords.x, shopCoords.y, shopCoords.z+1)
            if (GetDistanceBetweenCoords(coords, GetEntityCoords(PlayerPedId()), true) < 13.0) then
                distance = (GetDistanceBetweenCoords(coords, GetEntityCoords(PlayerPedId()), true))
                sleep = 0
                find = true
                targetShop = k
            end
        end
        if not find then
            targetShop = nil
            sleep = 3000
        end
        Citizen.Wait(3000)
    end
end)

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(sleep)
        if targetShop then
            local coords = vector3(STG.Shops[targetShop]["coord"].x, STG.Shops[targetShop]["coord"].y, STG.Shops[targetShop]["coord"].z+2)
            if distance < 13.0 then
                DrawMarker(20, coords - vector3(0.0, 0.0, 0.985), 0.0, 0.0, 0.0, 0, 0.0, 0.0,0.5, 0.5, 0.5, 0, 255, 255, 100, false, false, 2, true, false, false, false)
            end
            if distance < 2.0 then
                leftNotify(getMessage("open"), true, false, 2000)
                if IsControlJustPressed(0, 38) then
                    openMenu()
                end
            end
        end
    end
end)

RegisterNUICallback("updateCamera",function (data)
    changeCamera(data.type)
    updateMaxValue(data.id, data.prop, data.barber)
end)

RegisterNUICallback("exit",function (data)
    deleteCamera()
    SetNuiFocus(false, false)
    loadCurrentSkin()
    money = 0
    clothingList = {}
end)

RegisterNUICallback("buy",function (data)
    ESX.TriggerServerCallback('stg_clothing:getMoney', function(verify)
        if verify then
            save()
            exit()
            notify('success')
        else
            notify('noMoney')
        end
    end, data.type, money)
end)

RegisterNUICallback("changeClothe",function (data)
    updateList()
    if isProp then
        local max = GetNumberOfPedPropDrawableVariations(PlayerPedId(), lastClothe) - 1
        if max >= tonumber(data.value) then
            SetPedPropIndex(PlayerPedId(), lastClothe, tonumber(data.value), 0, 2)
            updateMaxValue(lastClothe, isProp)
        end
    else
        local max = GetNumberOfPedDrawableVariations(PlayerPedId(), lastClothe) - 1
        if max >= tonumber(data.value) then
            SetPedComponentVariation(PlayerPedId(), lastClothe, tonumber(data.value), 0, 2)
            updateMaxValue(lastClothe, isProp)
        end
    end
    updatePrice()
end)

RegisterNUICallback("changeColor",function (data)
    updateList()
    if isProp then
        local max = GetNumberOfPedPropDrawableVariations(PlayerPedId(), lastClothe) - 1
        if max >= tonumber(data.value) then
            SetPedPropIndex(PlayerPedId(), lastClothe, GetPedPropIndex(PlayerPedId(), lastClothe), tonumber(data.value), 0, 2)
            updateMaxValue(lastClothe, isProp)
        end
    else
        local max = GetNumberOfPedDrawableVariations(PlayerPedId(), lastClothe) - 1
        if max >= tonumber(data.value) then
            SetPedComponentVariation(PlayerPedId(), lastClothe, GetPedDrawableVariation(PlayerPedId(), lastClothe), tonumber(data.value), 0, 2)
            updateMaxValue(lastClothe, isProp)
        end
    end
    updatePrice()
end)


RegisterNUICallback("nextClothe",function ()
    updateList()
    if isBarber then
        if lastClothe == 2 then
            local current = GetPedDrawableVariation(PlayerPedId(), lastClothe)
            local max = GetNumberOfPedDrawableVariations(PlayerPedId(), lastClothe) - 1

            if max >= current+1 then
                SetPedComponentVariation(PlayerPedId(), lastClothe, current+1, 0, 2)
                updateMaxValue(lastClothe, isProp, isBarber)
            end
        else
            local current = GetPedHeadOverlayValue(PlayerPedId(), lastClothe)
            local max = GetPedHeadOverlayNum(lastClothe) - 1

            if max >= current+1 then
                SetPedHeadOverlay(PlayerPedId(), lastClothe, current+1, 10.0)
                updateMaxValue(lastClothe, isProp, isBarber)
            end
        end
    else
        if isProp then
            local current = GetPedPropIndex(PlayerPedId(), lastClothe)
            local max = GetNumberOfPedPropDrawableVariations(PlayerPedId(), lastClothe) - 1

            if max >= current+1 then
                SetPedPropIndex(PlayerPedId(), lastClothe, current+1, 0, 2)
                updateMaxValue(lastClothe, isProp)
            end
        else
            local current = GetPedDrawableVariation(PlayerPedId(), lastClothe)
            local max = GetNumberOfPedDrawableVariations(PlayerPedId(), lastClothe) - 1
            local bag = checkBag(max, true) 

            if max >= current+1 and bag then
                SetPedComponentVariation(PlayerPedId(), lastClothe, current+1, 0, 2)
                updateMaxValue(lastClothe, isProp)
            end
        end
    end
    updatePrice()
end)

RegisterNUICallback("backClothe",function ()
    updateList()
    if isBarber then
        if lastClothe == 2 then
            local current = GetPedDrawableVariation(PlayerPedId(), lastClothe)
            local max = GetNumberOfPedDrawableVariations(PlayerPedId(), lastClothe) - 1

            if current-1 >= 0 then
                SetPedComponentVariation(PlayerPedId(), lastClothe, current-1, 0, 2)
                updateMaxValue(lastClothe, isProp, isBarber)
            end
        else
            local current = GetPedHeadOverlayValue(PlayerPedId(), lastClothe)
            local max = GetPedHeadOverlayNum(lastClothe) - 1

            if current-1 >= 0 then
                SetPedHeadOverlay(PlayerPedId(), lastClothe, current-1, 10.0)
                updateMaxValue(lastClothe, isProp, isBarber)
            end
        end
    else
        if isProp then
            local current = GetPedPropIndex(PlayerPedId(), lastClothe)

            if current-1 >= 0 then
                SetPedPropIndex(PlayerPedId(), lastClothe, current-1, 0, 2)
                updateMaxValue(lastClothe, isProp)
            else
                ClearPedProp(PlayerPedId(), lastClothe)
                updateMaxValue(lastClothe, isProp)
            end
        else
            local current = GetPedDrawableVariation(PlayerPedId(), lastClothe)
            local bag = checkBag(nil, false) 

            if current-1 >= 0 and bag then
                SetPedComponentVariation(PlayerPedId(), lastClothe, current-1, 0, 2)
                updateMaxValue(lastClothe, isProp)
            end
        end
    end
    updatePrice()
end)

RegisterNUICallback("nextColor",function ()
    if isBarber then
        if lastClothe == 2 then
            local current = GetPedHairColor(PlayerPedId())
            local max = GetNumHairColors() - 1
            if max >= current+1 then
                SetPedHairColor(PlayerPedId(), current+1)
                updateMaxValue(lastClothe, isProp, isBarber)
            end
        else
            local a, b, c, current = GetPedHeadOverlayData(PlayerPedId(), lastClothe)
            local max = GetNumHairColors() - 1
            if max >= current+1 then
                SetPedHeadOverlayColor(PlayerPedId(), lastClothe, 1, current+1, 4.0)
                updateMaxValue(lastClothe, isProp, isBarber)
            end
        end
    else
        if isProp then
            local current = GetPedPropTextureIndex(PlayerPedId(), lastClothe)
            local max = GetNumberOfPedPropTextureVariations(PlayerPedId(), lastClothe, GetPedPropIndex(PlayerPedId(), lastClothe)) - 1

            if max >= current+1 then
                SetPedPropIndex(PlayerPedId(), lastClothe, GetPedPropIndex(PlayerPedId(), lastClothe), current+1, 0, 2)
                updateMaxValue(lastClothe, isProp)
            end
        else
            local current = GetPedTextureVariation(PlayerPedId(), lastClothe)
            local max = GetNumberOfPedTextureVariations(PlayerPedId(), lastClothe, GetPedDrawableVariation(PlayerPedId(), lastClothe)) - 1

            if max >= current+1 then
                SetPedComponentVariation(PlayerPedId(), lastClothe, GetPedDrawableVariation(PlayerPedId(), lastClothe), current+1, 0, 2)
                updateMaxValue(lastClothe, isProp)
            end
        end
    end
end)

RegisterNUICallback("backColor",function ()
    if isBarber then
        if lastClothe == 2 then
            local current = GetPedHairColor(PlayerPedId())
            local max = GetNumHairColors() - 1
            if current-1 >= 0 then
                SetPedHairColor(PlayerPedId(), current-1)
                updateMaxValue(lastClothe, isProp, isBarber)
            end
        else
            local a, b, c, current = GetPedHeadOverlayData(PlayerPedId(), lastClothe)
            local max = GetNumHairColors() - 1
                if current-1 >= 0 then
                SetPedHeadOverlayColor(PlayerPedId(), lastClothe, 1, current-1, 4.0)	
                updateMaxValue(lastClothe, isProp, isBarber)
            end
        end
    else
        if isProp then
            local current = GetPedPropTextureIndex(PlayerPedId(), lastClothe)

            if current-1 >= 0 then
                SetPedPropIndex(PlayerPedId(), lastClothe, GetPedPropIndex(PlayerPedId(), lastClothe), current-1, 0, 2)
                updateMaxValue(lastClothe, isProp)
            end
        else
            local current = GetPedTextureVariation(PlayerPedId(), lastClothe)

            if current-1 >= 0 then
                SetPedComponentVariation(PlayerPedId(), lastClothe, GetPedDrawableVariation(PlayerPedId(), lastClothe), current-1, 0, 2)
                updateMaxValue(lastClothe, isProp)
            end
        end
    end
end)

RegisterNUICallback("rotate",function (data)
    local myHeading = GetEntityHeading(PlayerPedId())
    if data.status == "left" then
        myHeading = myHeading-10.0
    else
        myHeading = myHeading + 10.0
    end
    SetEntityHeading(PlayerPedId(), myHeading)
end)

function openMenu()
    saveCurrentSkin()
    lastBag = GetPedDrawableVariation(PlayerPedId(), 5)
    SetNuiFocus(true, true)
    SendNUIMessage({
        type = "open",
        shopType = STG.Shops[targetShop].type
    })
    openCamera()
end

function checkBag(max, add)
    if lastClothe == 5 then
        if add then
            if max >= lastBag+1 then
                SetPedComponentVariation(PlayerPedId(), lastClothe, lastBag+1, 0, 2)
                updateMaxValue(lastClothe, isProp)
                lastBag = lastBag+1
                return false
            end
            return true
        else
            if lastBag-1 >= 0 then
                SetPedComponentVariation(PlayerPedId(), lastClothe, lastBag-1, 0, 2)
                updateMaxValue(lastClothe, isProp)
                lastBag = lastBag-1
                return false
            end
            return true
        end
    end
    return true
end

function getPrice(drawID, prop)
    for k,v in pairs(STG.prices) do
        local info = STG.prices[k]
        if info.componentID == drawID and prop == info.isProp then
            return info.price
        end
    end
end

function updateMaxValue(value, prop, barber)
    lastClothe = value
    isProp = prop
    isBarber = barber
    if prop then
        local current = GetPedPropIndex(PlayerPedId(), value)
        SendNUIMessage({
            type = "updateMax",
            current = current,
            currentColor = GetPedPropTextureIndex(PlayerPedId(), value),
            max = GetNumberOfPedPropDrawableVariations(PlayerPedId(), value) - 1,
            maxColor = GetNumberOfPedPropTextureVariations(PlayerPedId(), value, current) - 1
        })
    elseif barber then
        if value == 2 then
            local current = GetPedDrawableVariation(PlayerPedId(), value)
            SendNUIMessage({
                type = "updateMax",
                current = current,
                currentColor = GetPedHairColor(PlayerPedId()),
                max = GetNumberOfPedDrawableVariations(PlayerPedId(), value) - 1,
                maxColor = GetNumHairColors() - 1
            })
        else
            local current = GetPedHeadOverlayValue(PlayerPedId(), value)
            local a, b, c, currentColor = GetPedHeadOverlayData(PlayerPedId(), value)
            SendNUIMessage({
                type = "updateMax",
                current = current,
                currentColor = currentColor,
                max = GetPedHeadOverlayNum(value) - 1,
                maxColor = GetNumHairColors() - 1
            })
        end
    else
        local current = GetPedDrawableVariation(PlayerPedId(), value)
        SendNUIMessage({
            type = "updateMax",
            current = current,
            currentColor = GetPedTextureVariation(PlayerPedId(), value),
            max = GetNumberOfPedDrawableVariations(PlayerPedId(), value) - 1,
            maxColor = GetNumberOfPedTextureVariations(PlayerPedId(), value, current) - 1
        })
    end
end

function exit()
    SendNUIMessage({type = "exit"})
    deleteCamera()
    SetNuiFocus(false, false)
    money = 0
    clothingList = {}
end

function updateList()
    if not clothingList[lastClothe] then
        if not isProp then
            clothingList[lastClothe] = {default = GetPedDrawableVariation(PlayerPedId(), lastClothe)-1, isProp = false}
        else
            clothingList[lastClothe] = {default = GetPedPropIndex(PlayerPedId(), lastClothe)-1, isProp = true}
        end
    end
end

function updatePrice()
    if not isBarber then
        money = 0
        for k,v in pairs(clothingList) do
            local info = clothingList[k]
            if info.isProp then
                if GetPedPropIndex(PlayerPedId(), k)-1 ~= info.default then
                    money = money+getPrice(k, info.isProp)
                end
            else
                if GetPedDrawableVariation(PlayerPedId(), k)-1 ~= info.default then
                    money = money+getPrice(k, info.isProp)
                end 
            end
        end 
        SendNUIMessage({
            type = "updatePrice",
            price = money
        })
    end
end

function loadCurrentSkin()
    if not currentSkin then return end

    SetPedPropIndex(PlayerPedId(), 0, currentSkin['helmet_1'] or -1, currentSkin['helmet_2'] or 0)
    SetPedPropIndex(PlayerPedId(), 1, currentSkin['glasses_1'] or -1, currentSkin['glasses_2'] or 0)
    SetPedPropIndex(PlayerPedId(), 6, currentSkin['watches_1'] or -1, currentSkin['watches_2'] or 0)

    SetPedComponentVariation(PlayerPedId(), 8, currentSkin["tshirt_1"] or 0, currentSkin['tshirt_2'] or 0, 0, 2)
    SetPedComponentVariation(PlayerPedId(), 4, currentSkin["pants_1"] or 0, currentSkin['pants_2'] or 0, 0, 2)
    SetPedComponentVariation(PlayerPedId(), 6, currentSkin["shoes_1"] or 0, currentSkin['shoes_2'] or 0, 0, 2)
    SetPedComponentVariation(PlayerPedId(), 11, currentSkin["torso_1"] or 0, currentSkin['torso_2'] or 0, 0, 2)
    SetPedComponentVariation(PlayerPedId(), 1, currentSkin["mask_1"] or 0, currentSkin['mask_2'] or 0, 0, 2)
    SetPedComponentVariation(PlayerPedId(), 7, currentSkin["chain_1"] or 0, currentSkin['chain_2'] or 0, 0, 2)
    SetPedComponentVariation(PlayerPedId(), 5, currentSkin["bags_1"] or 0, currentSkin['bags_2'] or 0, 0, 2)
    SetPedComponentVariation(PlayerPedId(), 3, currentSkin["arms"] or 0, currentSkin['arms_2'] or 0, 0, 2)
    SetPedComponentVariation(PlayerPedId(), 9, currentSkin["bproof_1"] or 0, currentSkin['bproof_2'] or 0, 0, 2)
    SetPedComponentVariation(PlayerPedId(), 2, currentSkin["hair_1"] or 0, 0, 2)

    SetPedHairColor(PlayerPedId(), currentSkin["hair_color_1"] or 0)

    SetPedHeadOverlay(PlayerPedId(), 1, currentSkin["beard_1"] or 0, 10.0)
    SetPedHeadOverlayColor(PlayerPedId(), 1, 1, currentSkin["beard_3"] or 0, 4.0)

    SetPedHeadOverlay(PlayerPedId(), 4, currentSkin["makeup_1"] or 0, 10.0)
    SetPedHeadOverlayColor(PlayerPedId(), 4, 1, currentSkin["makeup_3"] or 0, 4.0)
end

function loadSkinFromData(skin)
    if not skin then return end

    -- بارگذاری لباس از دیتای ذخیره شده
    if skin.mask then
        SetPedComponentVariation(PlayerPedId(), 1, skin.mask.item or 0, skin.mask.texture or 0, 0, 2)
    end
    if skin.hat then
        SetPedPropIndex(PlayerPedId(), 0, skin.hat.item or -1, skin.hat.texture or 0)
    end
    if skin.accessory then
        SetPedComponentVariation(PlayerPedId(), 7, skin.accessory.item or 0, skin.accessory.texture or 0, 0, 2)
    end
    if skin.glass then
        SetPedPropIndex(PlayerPedId(), 1, skin.glass.item or -1, skin.glass.texture or 0)
    end
    if skin.bag then
        SetPedComponentVariation(PlayerPedId(), 5, skin.bag.item or 0, skin.bag.texture or 0, 0, 2)
    end
    if skin["t-shirt"] then
        SetPedComponentVariation(PlayerPedId(), 8, skin["t-shirt"].item or 0, skin["t-shirt"].texture or 0, 0, 2)
    end
    if skin.watch then
        SetPedPropIndex(PlayerPedId(), 6, skin.watch.item or -1, skin.watch.texture or 0)
    end
    if skin.arms then
        SetPedComponentVariation(PlayerPedId(), 3, skin.arms.item or 0, skin.arms.texture or 0, 0, 2)
    end
    if skin.pants then
        SetPedComponentVariation(PlayerPedId(), 4, skin.pants.item or 0, skin.pants.texture or 0, 0, 2)
    end
    if skin.shoes then
        SetPedComponentVariation(PlayerPedId(), 6, skin.shoes.item or 0, skin.shoes.texture or 0, 0, 2)
    end
    if skin.torso2 then
        SetPedComponentVariation(PlayerPedId(), 11, skin.torso2.item or 0, skin.torso2.texture or 0, 0, 2)
    end
    if skin.vest then
        SetPedComponentVariation(PlayerPedId(), 9, skin.vest.item or 0, skin.vest.texture or 0, 0, 2)
    end
    if skin.hair then
        SetPedComponentVariation(PlayerPedId(), 2, skin.hair.item or 0, 0, 2)
        SetPedHairColor(PlayerPedId(), skin.hair.texture or 0)
    end
    if skin.beard then
        SetPedHeadOverlay(PlayerPedId(), 1, skin.beard.item or 0, 10.0)
        SetPedHeadOverlayColor(PlayerPedId(), 1, 1, skin.beard.texture or 0, 4.0)
    end
    if skin.makeup then
        SetPedHeadOverlay(PlayerPedId(), 4, skin.makeup.item or 0, 10.0)
        SetPedHeadOverlayColor(PlayerPedId(), 4, 1, skin.makeup.texture or 0, 4.0)
    end
end

function saveCurrentSkin()
    currentSkin["sex"] = 0

    currentSkin["mask_1"] = GetPedDrawableVariation(PlayerPedId(), 1)
    currentSkin["mask_2"] = GetPedTextureVariation(PlayerPedId(), 1)

    currentSkin['helmet_1'] = GetPedPropIndex(PlayerPedId(), 0)
    currentSkin['helmet_2'] = GetPedPropTextureIndex(PlayerPedId(), 0)

    currentSkin['chain_1'] = GetPedDrawableVariation(PlayerPedId(), 7)
    currentSkin['chain_2'] = GetPedTextureVariation(PlayerPedId(), 7)

    currentSkin['glasses_1'] = GetPedPropIndex(PlayerPedId(), 1)
    currentSkin['glasses_2'] = GetPedPropIndex(PlayerPedId(), 1)

    currentSkin["bags_1"] = GetPedDrawableVariation(PlayerPedId(), 5)
    currentSkin["bags_2"] = GetPedTextureVariation(PlayerPedId(), 5)

    currentSkin["tshirt_1"] = GetPedDrawableVariation(PlayerPedId(), 8)
    currentSkin["tshirt_2"] = GetPedTextureVariation(PlayerPedId(), 8)

    currentSkin['watches_1'] = GetPedPropIndex(PlayerPedId(), 6)
    currentSkin['watches_2'] = GetPedPropIndex(PlayerPedId(), 6)

    currentSkin["arms"] = GetPedDrawableVariation(PlayerPedId(), 3)
    currentSkin["arms_2"] = GetPedTextureVariation(PlayerPedId(), 3)

    currentSkin["pants_1"] = GetPedDrawableVariation(PlayerPedId(), 4)
    currentSkin["pants_2"] = GetPedTextureVariation(PlayerPedId(), 4)

    currentSkin["shoes_1"] = GetPedDrawableVariation(PlayerPedId(), 6)
    currentSkin["shoes_2"] = GetPedTextureVariation(PlayerPedId(), 6)

    currentSkin["torso_1"] = GetPedDrawableVariation(PlayerPedId(), 11)
    currentSkin["torso_2"] = GetPedTextureVariation(PlayerPedId(), 11)

    currentSkin["bproof_1"] = GetPedDrawableVariation(PlayerPedId(), 9)
    currentSkin["bproof_2"] = GetPedTextureVariation(PlayerPedId(), 9)

    currentSkin["hair_1"] = GetPedDrawableVariation(PlayerPedId(), 2)
    currentSkin["hair_color_1"] = GetPedHairColor(PlayerPedId())

    local a, b, c, current = GetPedHeadOverlayData(PlayerPedId(), 1)
    currentSkin["beard_1"] = GetPedHeadOverlayValue(PlayerPedId(), 1)
    currentSkin["beard_2"] = 10
    currentSkin["beard_3"] = current

    local a, b, c, currentG = GetPedHeadOverlayData(PlayerPedId(), 4)
    currentSkin['makeup_1'] = GetPedHeadOverlayValue(PlayerPedId(), 4)
    currentSkin['makeup_2'] = 10.0
    currentSkin['makeup_3'] = currentG
    currentSkin['makeup_4'] = 3.0
end

function getGender()
    local model = GetEntityModel(PlayerPedId())
    if model == GetHashKey("mp_m_freemode_01") then
        return "male"
    else
        return "female"
    end
end

function save()
    ESX.TriggerServerCallback('stg_clothing:getSkin', function(data)
        local skin = {}

        -- بررسی اینکه آیا دیتا وجود دارد یا نه
        if data and data["skin"] then
            local success, decodedSkin = pcall(json.decode, data["skin"])
            if success and decodedSkin then
                skin = decodedSkin
            else
                print("^1[STG Clothing] Error: Failed to decode existing skin data, using default skin^7")
                skin = {
                    mask = {item = 0, texture = 0},
                    hat = {item = -1, texture = 0},
                    accessory = {item = 0, texture = 0},
                    glass = {item = -1, texture = 0},
                    bag = {item = 0, texture = 0},
                    ["t-shirt"] = {item = 0, texture = 0},
                    watch = {item = -1, texture = 0},
                    glove = {item = 0, texture = 0},
                    pants = {item = 0, texture = 0},
                    shoes = {item = 0, texture = 0},
                    torso = {item = 0, texture = 0},
                    makeup = {item = 0, texture = 0}
                }
            end
        else
            -- اگر دیتا وجود نداشت، یک اسکین پیش‌فرض ایجاد کن
            print("^3[STG Clothing] Warning: No existing skin data found for player, creating default skin^7")
            skin = {
                mask = {item = 0, texture = 0},
                hat = {item = -1, texture = 0},
                accessory = {item = 0, texture = 0},
                glass = {item = -1, texture = 0},
                bag = {item = 0, texture = 0},
                ["t-shirt"] = {item = 0, texture = 0},
                watch = {item = -1, texture = 0},
                glove = {item = 0, texture = 0},
                pants = {item = 0, texture = 0},
                shoes = {item = 0, texture = 0},
                torso = {item = 0, texture = 0},
                makeup = {item = 0, texture = 0}
            }
        end

        skin["sex"] = 0

        -- اطمینان از وجود ساختار مورد نیاز
        if not skin["mask"] or type(skin["mask"]) ~= "table" then skin["mask"] = {} end
        skin["mask"]["item"] = GetPedDrawableVariation(PlayerPedId(), 1)
        skin["mask"]["texture"] = GetPedTextureVariation(PlayerPedId(), 1)

        if not skin['hat'] or type(skin['hat']) ~= "table" then skin['hat'] = {} end
        skin['hat']["item"] = GetPedPropIndex(PlayerPedId(), 0)
        skin['hat']["texture"] = GetPedPropTextureIndex(PlayerPedId(), 0)

        if not skin['accessory'] or type(skin['accessory']) ~= "table" then skin['accessory'] = {} end
        skin['accessory']["item"] = GetPedDrawableVariation(PlayerPedId(), 7)
        skin['accessory']["texture"] = GetPedTextureVariation(PlayerPedId(), 7)

        if not skin['glass'] or type(skin['glass']) ~= "table" then skin['glass'] = {} end
        skin['glass']["item"] = GetPedPropIndex(PlayerPedId(), 1)
        skin['glass']["texture"] = GetPedPropIndex(PlayerPedId(), 1)

        if not skin["bag"] or type(skin["bag"]) ~= "table" then skin["bag"] = {} end
        skin["bag"]["item"] = GetPedDrawableVariation(PlayerPedId(), 5)
        skin["bag"]["texture"] = GetPedTextureVariation(PlayerPedId(), 5)

        if not skin["t-shirt"] or type(skin["t-shirt"]) ~= "table" then skin["t-shirt"] = {} end
        skin["t-shirt"]["item"] = GetPedDrawableVariation(PlayerPedId(), 8)
        skin["t-shirt"]["texture"] = GetPedTextureVariation(PlayerPedId(), 8)

        if not skin['watch'] or type(skin['watch']) ~= "table" then skin['watch'] = {} end
        skin['watch']["item"] = GetPedPropIndex(PlayerPedId(), 6)
        skin['watch']["texture"] = GetPedPropIndex(PlayerPedId(), 6)

        if not skin["arms"] or type(skin["arms"]) ~= "table" then skin["arms"] = {} end
        skin["arms"]["item"] = GetPedDrawableVariation(PlayerPedId(), 3)
        skin["arms"]["texture"] = GetPedTextureVariation(PlayerPedId(), 3)

        if not skin["pants"] or type(skin["pants"]) ~= "table" then skin["pants"] = {} end
        skin["pants"]["item"] = GetPedDrawableVariation(PlayerPedId(), 4)
        skin["pants"]["texture"] = GetPedTextureVariation(PlayerPedId(), 4)

        if not skin["shoes"] or type(skin["shoes"]) ~= "table" then skin["shoes"] = {} end
        skin["shoes"]["item"] = GetPedDrawableVariation(PlayerPedId(), 6)
        skin["shoes"]["texture"] = GetPedTextureVariation(PlayerPedId(), 6)

        if not skin["torso2"] or type(skin["torso2"]) ~= "table" then skin["torso2"] = {} end
        skin["torso2"]["item"] = GetPedDrawableVariation(PlayerPedId(), 11)
        skin["torso2"]["texture"] = GetPedTextureVariation(PlayerPedId(), 11)

        if not skin["vest"] or type(skin["vest"]) ~= "table" then skin["vest"] = {} end
        skin["vest"]["item"] = GetPedDrawableVariation(PlayerPedId(), 9)
        skin["vest"]["texture"] = GetPedTextureVariation(PlayerPedId(), 9)

        if not skin["hair"] or type(skin["hair"]) ~= "table" then skin["hair"] = {} end
        skin["hair"]["item"] = GetPedDrawableVariation(PlayerPedId(), 2)
        skin["hair"]["texture"] = GetPedHairColor(PlayerPedId())

        if not skin["beard"] or type(skin["beard"]) ~= "table" then skin["beard"] = {} end
        local a, b, c, current = GetPedHeadOverlayData(PlayerPedId(), 1)
        skin["beard"]["item"] = GetPedHeadOverlayValue(PlayerPedId(), 1)
        skin["beard"]["texture"] = current

        if not skin['makeup'] or type(skin['makeup']) ~= "table" then skin['makeup'] = {} end
        local a, b, c, currentG = GetPedHeadOverlayData(PlayerPedId(), 4)
        skin['makeup']["item"] = GetPedHeadOverlayValue(PlayerPedId(), 4)
        skin['makeup']["texture"] = currentG

        local model = GetEntityModel(PlayerPedId())
        local clothing = json.encode(skin)
        TriggerServerEvent("stg_clothing:saveSkin", model, clothing)
    end)
end

function changeCamera(camera)
    if camera == "face" then
        zoomOffset = 0.6
        camOffset = 0.65
    elseif camera == "top" then
        zoomOffset = 0.75
        camOffset = 0.15
    elseif camera == "shoes" then
        zoomOffset = 0.8
        camOffset = -0.8
    elseif camera == "pants" then
        zoomOffset = 0.8
        camOffset = -0.5        
    end
end

function openCamera()
    createCamera()
    while isCameraActive do
     Citizen.Wait(0)
        DisableControlAction(2, 30, true)
        DisableControlAction(2, 31, true)
        DisableControlAction(2, 32, true)
        DisableControlAction(2, 33, true)
        DisableControlAction(2, 34, true)
        DisableControlAction(2, 35, true)
        DisableControlAction(0, 25, true) -- Input Aim
        DisableControlAction(0, 24, true) -- Input Attack

        local playerPed = PlayerPedId()
        local coords    = GetEntityCoords(playerPed)

        local angle = heading * math.pi / 180.0
        local theta = {
            x = math.cos(angle),
            y = math.sin(angle)
        }

        local pos = {
            x = coords.x + (zoomOffset * theta.x),
            y = coords.y + (zoomOffset * theta.y)
        }

        local angleToLook = heading - 140.0
        if angleToLook > 360 then
            angleToLook = angleToLook - 360
        elseif angleToLook < 0 then
            angleToLook = angleToLook + 360
        end

        angleToLook = angleToLook * math.pi / 180.0
        local thetaToLook = {
            x = math.cos(angleToLook),
            y = math.sin(angleToLook)
        }

        local posToLook = {
            x = coords.x + (zoomOffset * thetaToLook.x),
            y = coords.y + (zoomOffset * thetaToLook.y)
        }
        SetCamCoord(cam, pos.x, pos.y, coords.z + camOffset)
        PointCamAtCoord(cam, posToLook.x, posToLook.y, coords.z + camOffset)
    end
end

function createCamera()
    if not DoesCamExist(cam) then
        cam = CreateCam('DEFAULT_SCRIPTED_CAMERA', true)
    end

    local playerPed = PlayerPedId()

    SetCamActive(cam, true)
    RenderScriptCams(true, true, 500, true, true)

    isCameraActive = true
    SetCamCoord(cam, GetEntityCoords(playerPed))
    SetCamRot(cam, 0.0, 0.0, 270.0, true)
    isCameraActive = true
end

function deleteCamera()
    isCameraActive = false
    SetCamActive(cam, false)
    RenderScriptCams(false, true, 500, true, true)
    cam = nil
end

RegisterNUICallback("saveOutfit",function (data)
    -- ذخیره اسکین فعلی به جای استفاده از skinchanger
    local skin = {}
    saveCurrentSkin()
    for k, v in pairs(currentSkin) do
        skin[k] = v
    end
    TriggerServerEvent('stg_clothing:saveOutfit', data.name, skin)
    notify('saved')
end)

Citizen.CreateThread(function()
    if STG.wardrobe then
        RegisterCommand(STG.wardrobeCommand, function ()
            loadWardrobe()
            SendNUIMessage({
                type = "openWardrobe"
            })
            SetNuiFocus(true, true)
        end)
    end
end)

function loadWardrobe()
    ESX.TriggerServerCallback('stg_clothing:getWardrobe', function(data)
        for k,v in pairs(data) do
            SendNUIMessage({
                type = "addWardrobe",
                name = v.name,
                id = v.id
            })
        end
    end)
end

RegisterNUICallback("deleteWardrobe",function (data)
    TriggerServerEvent('stg_clothing:deleteWardrobe', data.id)
end)

RegisterNUICallback("useWardrobe",function (data)
    ESX.TriggerServerCallback('stg_clothing:getOutfit', function(result)
        if result and result.skin then
            local success, skin = pcall(json.decode, result.skin)
            if success and skin then
                -- بارگذاری اسکین بدون استفاده از skinchanger
                loadSkinFromData(skin)
            else
                print("^1[STG Clothing] Error: Failed to decode skin data for outfit ID " .. data.id)
            end
        else
            print("^3[STG Clothing] Warning: No outfit data found for ID " .. data.id)
        end
    end, data.id)
end)

RegisterNetEvent('stg_clothing:refreshWardrobe')
AddEventHandler('stg_clothing:refreshWardrobe', function ()
    loadWardrobe()
end)