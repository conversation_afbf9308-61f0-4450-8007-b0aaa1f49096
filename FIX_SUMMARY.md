# STG Clothing - Nil Data Error Fix

## مشکل اصلی (Original Problem)
```
SCRIPT ERROR: @stg_clothing/main/client.lua:581: attempt to index a nil value (local 'data')
```

## علت مشکل (Root Cause)
در تابع `save()` در خط 581، کد سعی می‌کرد به `data["skin"]` دسترسی پیدا کند، اما اگر بازیکن هیچ اسکین ذخیره شده‌ای در جدول `playerskins` نداشت، `data` برابر با `nil` بود.

## تغییرات انجام شده (Changes Made)

### 1. فیکس تابع `save()` در `main/client.lua`
- اضافه کردن بررسی `nil` برای `data`
- اضافه کردن بررسی `nil` برای `data["skin"]`
- ایجاد اسکین پیش‌فرض در صورت عدم وجود دیتا
- اضافه کردن `pcall` برای `json.decode` جهت جلوگیری از خرابی JSON
- اضافه کردن بررسی `nil` برای تمام بخش‌های اسکین

### 2. بهبود callback سرور در `main/server.lua`
- اضافه کردن بررسی `xPlayer` برای جلوگیری از خطا
- اضافه کردن بررسی `result` و `#result > 0`
- اضافه کردن پیام‌های لاگ برای دیباگ

### 3. بهبود `useWardrobe` callback در `main/client.lua`
- اضافه کردن بررسی `result` و `result.skin`
- اضافه کردن `pcall` برای `json.decode`
- اضافه کردن پیام‌های خطا مناسب

## کد قبل از فیکس (Before Fix)
```lua
function save()
    ESX.TriggerServerCallback('stg_clothing:getSkin', function(data)
        local skin = json.decode(data["skin"])  -- خطا: data ممکن است nil باشد
        -- ...
    end)
end
```

## کد بعد از فیکس (After Fix)
```lua
function save()
    ESX.TriggerServerCallback('stg_clothing:getSkin', function(data)
        local skin = {}
        
        if data and data["skin"] then
            local success, decodedSkin = pcall(json.decode, data["skin"])
            if success and decodedSkin then
                skin = decodedSkin
            else
                -- اسکین پیش‌فرض
                skin = { ... }
            end
        else
            -- اسکین پیش‌فرض
            skin = { ... }
        end
        
        -- بررسی وجود هر بخش قبل از استفاده
        if not skin["mask"] then skin["mask"] = {} end
        -- ...
    end)
end
```

## تست (Testing)
برای تست فیکس، فایل `test_fix.lua` ایجاد شده که شامل دستورات زیر است:
- `/test_stg_fix` - تست callback ها
- `/test_stg_save` - تست تابع save

## نتیجه (Result)
- خطای "attempt to index a nil value" برطرف شد
- سیستم حالا برای بازیکنان جدید که هیچ اسکین ذخیره شده‌ای ندارند کار می‌کند
- اسکین پیش‌فرض به صورت خودکار ایجاد می‌شود
- خطاهای JSON decode نیز مدیریت می‌شوند
