# STG Clothing - Money System Fix

## مشکلات قبلی (Previous Issues)
1. **getAccount Error**: خطا در دسترسی به حساب بانک در ESX های جدید
2. **removeAccountMoney Error**: خطا در کم کردن پول از بانک
3. **Money Deduction**: پول کم میشد حتی اگر save موفق نبود

## تغییرات انجام شده (Changes Made)

### 1. فیکس سازگاری ESX در `main/server.lua`

#### بخش Cash (پول نقد):
```lua
// قبل
if xPlayer.removeMoney then
    xPlayer.removeMoney(money)
end

// بعد
if xPlayer.removeMoney then
    success = xPlayer.removeMoney(money)
    if success == nil then success = true end
elseif xPlayer.money then
    xPlayer.money = xPlayer.money - money
    success = true
end
```

#### بخش Bank (پول بانک):
```lua
// قبل
if xPlayer.getAccount then
    bankAccount = xPlayer.getAccount('bank')
end

// بعد
// روش جدید ESX
if xPlayer.getAccount then
    local bankAccount = xPlayer.getAccount('bank')
    if bankAccount then
        bankMoney = bankAccount.money or 0
    end
// روش قدیمی ESX
elseif xPlayer.accounts then
    for i=1, #xPlayer.accounts do
        if xPlayer.accounts[i].name == 'bank' then
            bankMoney = xPlayer.accounts[i].money or 0
            break
        end
    end
end
```

### 2. اضافه کردن Debug Logs
- لاگ مقدار پول فعلی بازیکن
- لاگ موفقیت/شکست عملیات کم کردن پول
- پیام‌های واضح برای دیباگ

### 3. فایل‌های تست جدید

#### `money_debug.lua`:
- `/add_money <cash/bank> <amount>` - اضافه کردن پول
- `/remove_money <cash/bank> <amount>` - تست کم کردن پول
- `/check_money` - چک کردن پول فعلی

#### `test_fix.lua` (آپدیت شده):
- `/test_stg_money` - تست سیستم پول

### 4. Event های جدید در سرور
- `stg_clothing:checkMoney` - چک کردن پول بازیکن
- `stg_clothing:addMoney` - اضافه کردن پول (برای تست)

## نحوه تست (How to Test)

### 1. چک کردن پول فعلی:
```
/check_money
```

### 2. اضافه کردن پول برای تست:
```
/add_money cash 5000
/add_money bank 10000
```

### 3. تست سیستم خرید:
```
/test_stg_money
```

### 4. تست کم کردن پول:
```
/remove_money cash 100
/remove_money bank 200
```

## مشکلات برطرف شده (Fixed Issues)
✅ خطای `getAccount` در ESX های جدید
✅ خطای `removeAccountMoney` 
✅ سازگاری با ESX قدیمی و جدید
✅ Debug logs برای تشخیص مشکل
✅ تست commands برای بررسی عملکرد

## نکات مهم (Important Notes)
- سیستم حالا با هر دو نسخه ESX قدیمی و جدید کار میکنه
- Debug logs کمک میکنه مشکل رو سریع پیدا کنی
- تست commands رو بعد از حل مشکل حذف کن
