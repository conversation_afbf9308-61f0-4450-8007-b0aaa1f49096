-- Test script to verify the nil data fix
-- این اسکریپت برای تست فیکس مشکل nil data است

-- تست کردن callback بدون دیتا
RegisterCommand('test_stg_fix', function(source, args, rawCommand)
    print("^2[STG Test] Testing clothing system fix...^7")
    
    -- تست callback گرفتن اسکین
    ESX.TriggerServerCallback('stg_clothing:getSkin', function(data)
        if data then
            print("^2[STG Test] ✓ getSkin callback returned data^7")
            if data.skin then
                print("^2[STG Test] ✓ Skin data exists^7")
            else
                print("^3[STG Test] ! No skin field in data^7")
            end
        else
            print("^3[STG Test] ! getSkin callback returned nil (this is expected for new players)^7")
        end
    end)
    
    -- تست callback گرفتن کمد لباس
    ESX.TriggerServerCallback('stg_clothing:getWardrobe', function(data)
        if data then
            print("^2[STG Test] ✓ getWardrobe callback returned " .. #data .. " items^7")
        else
            print("^3[STG Test] ! getWardrobe callback returned nil^7")
        end
    end)
    
    print("^2[STG Test] Test completed. Check console for results.^7")
end, false)

-- تست دستی تابع save
RegisterCommand('test_stg_save', function(source, args, rawCommand)
    print("^2[STG Test] Testing save function...^7")
    
    -- فراخوانی مستقیم تابع save برای تست
    if save then
        save()
        print("^2[STG Test] ✓ Save function executed without errors^7")
    else
        print("^1[STG Test] ✗ Save function not found^7")
    end
end, false)

-- تست سیستم پول
RegisterCommand('test_stg_money', function(source, args, rawCommand)
    print("^2[STG Test] Testing money system...^7")

    -- تست پول نقد
    ESX.TriggerServerCallback('stg_clothing:getMoney', function(result)
        if result then
            print("^2[STG Test] ✓ Cash payment successful^7")
        else
            print("^1[STG Test] ✗ Cash payment failed^7")
        end
    end, "cash", 100)

    -- تست پول بانک
    ESX.TriggerServerCallback('stg_clothing:getMoney', function(result)
        if result then
            print("^2[STG Test] ✓ Bank payment successful^7")
        else
            print("^1[STG Test] ✗ Bank payment failed^7")
        end
    end, "bank", 100)
end, false)

-- چک کردن پول بازیکن
RegisterCommand('check_money', function(source, args, rawCommand)
    TriggerServerEvent('stg_clothing:checkMoney')
end, false)

-- دستور ساده برای تست getAccount
RegisterCommand('test_getaccount', function(source, args, rawCommand)
    print("^2[STG Test] Testing getAccount method...^7")

    ESX.TriggerServerCallback('stg_clothing:getMoney', function(result)
        if result then
            print("^2[STG Test] ✓ Bank access successful^7")
        else
            print("^1[STG Test] ✗ Bank access failed^7")
        end
    end, "bank", 1) -- تست با مبلغ کم
end, false)

print("^2[STG Test] Test commands loaded:^7")
print("^2  /test_stg_fix - Test callbacks^7")
print("^2  /test_stg_save - Test save function^7")
print("^2  /test_stg_money - Test money system^7")
print("^2  /check_money - Check player money^7")
print("^2  /test_getaccount - Test bank account access^7")
