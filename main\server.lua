ESX = nil

TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)

-- توابع کمکی برای سازگاری ESX
function GetPlayerCash(xPlayer)
    if not xPlayer then return 0 end

    if xPlayer.getMoney then
        return xPlayer.getMoney()
    elseif xPlayer.money then
        return xPlayer.money
    else
        return 0
    end
end

function GetPlayerBank(xPlayer)
    if not xPlayer then return 0 end

    -- روش جدید ESX
    if xPlayer.getAccount then
        local bankAccount = xPlayer.getAccount('bank')
        if bankAccount then
            return bankAccount.money or 0
        end
    end

    -- روش قدیمی ESX
    if xPlayer.accounts then
        for i=1, #xPlayer.accounts do
            if xPlayer.accounts[i].name == 'bank' then
                return xPlayer.accounts[i].money or 0
            end
        end
    end

    return 0
end

function RemovePlayerCash(xPlayer, amount)
    if not xPlayer or not amount or amount <= 0 then return false end

    local currentMoney = GetPlayerCash(xPlayer)
    if currentMoney < amount then return false end

    if xPlayer.removeMoney then
        local result = xPlayer.removeMoney(amount)
        return result ~= false -- برخی ESX ها nil برمیگردونن
    elseif xPlayer.money then
        xPlayer.money = xPlayer.money - amount
        return true
    end

    return false
end

function RemovePlayerBank(xPlayer, amount)
    if not xPlayer or not amount or amount <= 0 then return false end

    local currentMoney = GetPlayerBank(xPlayer)
    if currentMoney < amount then return false end

    -- روش جدید ESX
    if xPlayer.removeAccountMoney then
        local result = xPlayer.removeAccountMoney('bank', amount)
        return result ~= false -- برخی ESX ها nil برمیگردونن
    end

    -- روش قدیمی ESX
    if xPlayer.accounts then
        for i=1, #xPlayer.accounts do
            if xPlayer.accounts[i].name == 'bank' then
                if xPlayer.accounts[i].money >= amount then
                    xPlayer.accounts[i].money = xPlayer.accounts[i].money - amount
                    return true
                end
                break
            end
        end
    end

    return false
end

function AddPlayerCash(xPlayer, amount)
    if not xPlayer or not amount or amount <= 0 then return false end

    if xPlayer.addMoney then
        xPlayer.addMoney(amount)
        return true
    elseif xPlayer.money then
        xPlayer.money = xPlayer.money + amount
        return true
    end

    return false
end

function AddPlayerBank(xPlayer, amount)
    if not xPlayer or not amount or amount <= 0 then return false end

    -- روش جدید ESX
    if xPlayer.addAccountMoney then
        xPlayer.addAccountMoney('bank', amount)
        return true
    end

    -- روش قدیمی ESX
    if xPlayer.accounts then
        for i=1, #xPlayer.accounts do
            if xPlayer.accounts[i].name == 'bank' then
                xPlayer.accounts[i].money = xPlayer.accounts[i].money + amount
                return true
            end
        end
    end

    return false
end

-- callback گرفتن لیست لباس‌ها از stg_clothing
ESX.RegisterServerCallback('stg_clothing:getWardrobe', function(source, cb)
    local xPlayer = ESX.GetPlayerFromId(source)
    MySQL.query("SELECT * FROM stg_clothing WHERE owner = ?", {
        xPlayer.identifier
    }, function(result)
        cb(result)
    end)
end)

-- callback گرفتن یک لباس خاص از stg_clothing با id
ESX.RegisterServerCallback('stg_clothing:getOutfit', function(source, cb, id)
    local xPlayer = ESX.GetPlayerFromId(source)
    MySQL.query("SELECT * FROM stg_clothing WHERE id = ?", {
        id
    }, function(result)
        cb(result[1])
    end)
end)

-- callback بررسی و کم‌کردن پول برای خرید لباس
ESX.RegisterServerCallback('stg_clothing:getMoney', function(source, cb, type, money)
    local xPlayer = ESX.GetPlayerFromId(source)

    if not xPlayer then
        print("^1[STG Clothing] Error: Player not found for source " .. source)
        cb(false)
        return
    end

    if type == "cash" then
        -- استفاده از تابع کمکی برای پول نقد
        local playerMoney = GetPlayerCash(xPlayer)

        print("^3[STG Clothing] Debug: Player cash: " .. playerMoney .. ", Required: " .. money .. "^7")

        if playerMoney >= money then
            local success = RemovePlayerCash(xPlayer, money)

            if success then
                print("^2[STG Clothing] Successfully removed " .. money .. " cash^7")
                cb(true)
            else
                print("^1[STG Clothing] Failed to remove cash^7")
                cb(false)
            end
        else
            print("^1[STG Clothing] Insufficient cash^7")
            cb(false)
        end
    else
        -- استفاده از تابع کمکی برای پول بانک
        local bankMoney = GetPlayerBank(xPlayer)

        print("^3[STG Clothing] Debug: Player bank money: " .. bankMoney .. ", Required: " .. money .. "^7")

        if bankMoney >= money then
            local success = RemovePlayerBank(xPlayer, money)

            if success then
                print("^2[STG Clothing] Successfully removed " .. money .. " from bank^7")
                cb(true)
            else
                print("^1[STG Clothing] Failed to remove money from bank^7")
                cb(false)
            end
        else
            print("^1[STG Clothing] Insufficient bank funds^7")
            cb(false)
        end
    end
end)

-- ذخیره لباس جدید در stg_clothing
RegisterNetEvent('stg_clothing:saveOutfit')
AddEventHandler('stg_clothing:saveOutfit', function(name, skin)
    local xPlayer = ESX.GetPlayerFromId(source)
    MySQL.insert("INSERT INTO stg_clothing (owner, skin, name) VALUES (?, ?, ?)", {
        xPlayer.identifier,
        json.encode(skin),
        name
    }, function(insertId)
        -- اختیاری: می‌تونی اینجا پیامی برای کلاینت بفرستی
    end)
end)

-- حذف لباس از کمد
RegisterNetEvent('stg_clothing:deleteWardrobe')
AddEventHandler('stg_clothing:deleteWardrobe', function(id)
    local src = source
    MySQL.query("DELETE FROM `stg_clothing` WHERE `id` = ?", {
        id
    }, function(affectedRows)
        TriggerClientEvent('stg_clothing:refreshWardrobe', src)
    end)
end)

-- event برای چک کردن پول بازیکن (برای دیباگ)
RegisterNetEvent('stg_clothing:checkMoney')
AddEventHandler('stg_clothing:checkMoney', function()
    local src = source
    local xPlayer = ESX.GetPlayerFromId(src)

    if not xPlayer then
        print("^1[STG Clothing] Error: Player not found^7")
        return
    end

    -- استفاده از توابع کمکی
    local cash = GetPlayerCash(xPlayer)
    local bank = GetPlayerBank(xPlayer)

    print("^2[STG Clothing] Player " .. xPlayer.identifier .. " money:^7")
    print("^2  Cash: $" .. cash .. "^7")
    print("^2  Bank: $" .. bank .. "^7")

    TriggerClientEvent('chat:addMessage', src, {
        color = {0, 255, 0},
        multiline = true,
        args = {"STG Money", "Cash: $" .. cash .. " | Bank: $" .. bank}
    })
end)

-- event برای اضافه کردن پول (برای تست)
RegisterNetEvent('stg_clothing:addMoney')
AddEventHandler('stg_clothing:addMoney', function(moneyType, amount)
    local src = source
    local xPlayer = ESX.GetPlayerFromId(src)

    if not xPlayer then
        print("^1[STG Clothing] Error: Player not found^7")
        return
    end

    if moneyType == "cash" then
        local success = AddPlayerCash(xPlayer, amount)
        if success then
            print("^2[STG Clothing] Added $" .. amount .. " cash to player^7")
        else
            print("^1[STG Clothing] Failed to add cash^7")
        end
    elseif moneyType == "bank" then
        local success = AddPlayerBank(xPlayer, amount)
        if success then
            print("^2[STG Clothing] Added $" .. amount .. " to bank^7")
        else
            print("^1[STG Clothing] Failed to add bank money^7")
        end
    end

    TriggerClientEvent('chat:addMessage', src, {
        color = {0, 255, 0},
        multiline = true,
        args = {"STG Money", "Added $" .. amount .. " " .. moneyType}
    })
end)

-- callback گرفتن اسکین فعال از جدول playerskins
ESX.RegisterServerCallback('stg_clothing:getSkin', function(source, cb, method)
    local xPlayer = ESX.GetPlayerFromId(source)

    if not xPlayer then
        print("^1[STG Clothing] Error: Player not found for source " .. source)
        cb(nil)
        return
    end

    MySQL.query('SELECT * FROM playerskins WHERE identifier = ? AND active = 1', {
        xPlayer.identifier
    }, function(result)
        if result and #result > 0 then
            cb(result[1])
        else
            print("^3[STG Clothing] Warning: No active skin found for player " .. xPlayer.identifier .. ", returning nil^7")
            cb(nil)
        end
    end)
end)

-- ذخیره اسکین جدید به عنوان active
RegisterServerEvent("stg_clothing:saveSkin")
AddEventHandler("stg_clothing:saveSkin", function(model, skin)
    local src = source
    local Player = ESX.GetPlayerFromId(src)
    if model ~= nil and skin ~= nil then
        MySQL.query('DELETE FROM playerskins WHERE identifier = ?', {
            Player.identifier
        }, function()
            MySQL.insert('INSERT INTO playerskins (identifier, model, skin, active) VALUES (?, ?, ?, 1)', {
                Player.identifier,
                model,
                skin
            })
        end)
    end
end)
