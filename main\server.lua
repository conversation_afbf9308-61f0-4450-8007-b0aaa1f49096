ESX = nil

-- سازگاری با ESX کاستوم
Citizen.CreateThread(function()
    while ESX == nil do
        TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)
        Citizen.Wait(0)
    end
    print("^2[STG Clothing] ESX loaded successfully^7")
end)

-- ESX Compatibility Functions
function GetPlayerCash(xPlayer)
    if not xPlayer then return 0 end

    if xPlayer.getMoney then
        return xPlayer.getMoney()
    elseif xPlayer.money then
        return xPlayer.money
    else
        return 0
    end
end

function GetPlayerBank(xPlayer)
    if not xPlayer then return 0 end

    if xPlayer.getAccount then
        local bankAccount = xPlayer.getAccount('bank')
        if bankAccount then
            return bankAccount.money or 0
        end
    end

    if xPlayer.accounts then
        for i=1, #xPlayer.accounts do
            if xPlayer.accounts[i].name == 'bank' then
                return xPlayer.accounts[i].money or 0
            end
        end
    end

    return 0
end

function RemovePlayerCash(xPlayer, amount)
    if not xPlayer or not amount or amount <= 0 then return false end

    local currentMoney = GetPlayerCash(xPlayer)
    if currentMoney < amount then return false end

    if xPlayer.removeMoney then
        local result = xPlayer.removeMoney(amount)
        return result ~= false
    elseif xPlayer.money then
        xPlayer.money = xPlayer.money - amount
        return true
    end

    return false
end

function RemovePlayerBank(xPlayer, amount)
    if not xPlayer or not amount or amount <= 0 then return false end

    local currentMoney = GetPlayerBank(xPlayer)
    if currentMoney < amount then return false end

    if xPlayer.removeAccountMoney then
        local result = xPlayer.removeAccountMoney('bank', amount)
        return result ~= false
    end
    if xPlayer.accounts then
        for i=1, #xPlayer.accounts do
            if xPlayer.accounts[i].name == 'bank' then
                if xPlayer.accounts[i].money >= amount then
                    xPlayer.accounts[i].money = xPlayer.accounts[i].money - amount
                    return true
                end
                break
            end
        end
    end

    return false
end

function AddPlayerCash(xPlayer, amount)
    if not xPlayer or not amount or amount <= 0 then return false end

    if xPlayer.addMoney then
        xPlayer.addMoney(amount)
        return true
    elseif xPlayer.money then
        xPlayer.money = xPlayer.money + amount
        return true
    end

    return false
end

function AddPlayerBank(xPlayer, amount)
    if not xPlayer or not amount or amount <= 0 then return false end

    if xPlayer.addAccountMoney then
        xPlayer.addAccountMoney('bank', amount)
        return true
    end
    if xPlayer.accounts then
        for i=1, #xPlayer.accounts do
            if xPlayer.accounts[i].name == 'bank' then
                xPlayer.accounts[i].money = xPlayer.accounts[i].money + amount
                return true
            end
        end
    end

    return false
end


ESX.RegisterServerCallback('stg_clothing:getWardrobe', function(source, cb)
    local xPlayer = ESX.GetPlayerFromId(source)
    MySQL.query("SELECT * FROM stg_clothing WHERE owner = ?", {
        xPlayer.identifier
    }, function(result)
        cb(result)
    end)
end)


ESX.RegisterServerCallback('stg_clothing:getOutfit', function(source, cb, id)
    local xPlayer = ESX.GetPlayerFromId(source)
    MySQL.query("SELECT * FROM stg_clothing WHERE id = ?", {
        id
    }, function(result)
        cb(result[1])
    end)
end)


ESX.RegisterServerCallback('stg_clothing:getMoney', function(source, cb, type, money)
    -- صبر کردن تا ESX آماده بشه
    while ESX == nil do
        Citizen.Wait(100)
    end

    -- تلاش برای گرفتن player از ESX کاستوم
    local xPlayer = ESX.GetPlayerFromId(source)

    if not xPlayer then
        -- تلاش مجدد با تاخیر بیشتر
        Citizen.Wait(1000)
        xPlayer = ESX.GetPlayerFromId(source)

        if not xPlayer then
            print("^1[STG Clothing] ✗ Could not get player from ESX in getMoney^7")
            cb(false)
            return
        end
    end

    if type == "cash" then
        local playerMoney = GetPlayerCash(xPlayer)

        if playerMoney >= money then
            local success = RemovePlayerCash(xPlayer, money)
            cb(success)
        else
            cb(false)
        end
    else
        local bankMoney = GetPlayerBank(xPlayer)

        if bankMoney >= money then
            local success = RemovePlayerBank(xPlayer, money)
            cb(success)
        else
            cb(false)
        end
    end
end)


RegisterNetEvent('stg_clothing:saveOutfit')
AddEventHandler('stg_clothing:saveOutfit', function(name, skin)
    local xPlayer = ESX.GetPlayerFromId(source)
    MySQL.insert("INSERT INTO stg_clothing (owner, skin, name) VALUES (?, ?, ?)", {
        xPlayer.identifier,
        json.encode(skin),
        name
    }, function(insertId)

    end)
end)


RegisterNetEvent('stg_clothing:deleteWardrobe')
AddEventHandler('stg_clothing:deleteWardrobe', function(id)
    local src = source
    MySQL.query("DELETE FROM `stg_clothing` WHERE `id` = ?", {
        id
    }, function(affectedRows)
        TriggerClientEvent('stg_clothing:refreshWardrobe', src)
    end)
end)




ESX.RegisterServerCallback('stg_clothing:getSkin', function(source, cb, method)
    -- صبر کردن تا ESX آماده بشه
    while ESX == nil do
        Citizen.Wait(100)
    end

    -- تلاش برای گرفتن player از ESX کاستوم
    local xPlayer = ESX.GetPlayerFromId(source)
    local playerIdentifier = nil

    if xPlayer and xPlayer.identifier then
        playerIdentifier = xPlayer.identifier
    else
        -- روش جایگزین: گرفتن identifier مستقیماً
        for i = 0, GetNumPlayerIdentifiers(source) - 1 do
            local identifier = GetPlayerIdentifier(source, i)
            if string.find(identifier, "steam:") then
                playerIdentifier = identifier
                break
            elseif string.find(identifier, "license:") then
                playerIdentifier = identifier
            end
        end
    end

    if not playerIdentifier then
        print("^1[STG Clothing] ✗ Could not get player identifier in getSkin^7")
        cb(nil)
        return
    end

    MySQL.query('SELECT skin FROM users WHERE identifier = ?', {
        playerIdentifier
    }, function(result)
        if result and #result > 0 and result[1].skin then
            local skinData = {
                skin = result[1].skin
            }
            cb(skinData)
        else
            cb(nil)
        end
    end)
end)


RegisterServerEvent("stg_clothing:saveSkin")
AddEventHandler("stg_clothing:saveSkin", function(model, skin)
    local src = source

    print("^3[STG Clothing] SaveSkin called for source: " .. tostring(src) .. "^7")
    print("^3[STG Clothing] Model: " .. tostring(model) .. "^7")
    print("^3[STG Clothing] Skin data length: " .. (skin and string.len(skin) or "nil") .. "^7")

    if model == nil or skin == nil then
        print("^1[STG Clothing] ✗ Invalid data - model or skin is nil^7")
        return
    end

    -- صبر کردن تا ESX آماده بشه
    while ESX == nil do
        Citizen.Wait(100)
    end

    -- تلاش برای گرفتن player از ESX کاستوم
    local xPlayer = ESX.GetPlayerFromId(src)
    local playerIdentifier = nil

    if xPlayer and xPlayer.identifier then
        playerIdentifier = xPlayer.identifier
        print("^2[STG Clothing] ✓ Got identifier from ESX: " .. playerIdentifier .. "^7")
    else
        print("^3[STG Clothing] ESX player not found, trying manual identifier...^7")

        -- روش جایگزین: گرفتن identifier مستقیماً
        for i = 0, GetNumPlayerIdentifiers(src) - 1 do
            local identifier = GetPlayerIdentifier(src, i)
            if string.find(identifier, "steam:") then
                playerIdentifier = identifier
                break
            elseif string.find(identifier, "license:") then
                playerIdentifier = identifier
            end
        end
    end

    if not playerIdentifier then
        print("^1[STG Clothing] ✗ Could not get player identifier^7")
        return
    end

    print("^3[STG Clothing] Using identifier: " .. playerIdentifier .. "^7")
    print("^3[STG Clothing] Attempting to save skin...^7")

    MySQL.update('UPDATE users SET skin = ? WHERE identifier = ?', {
        skin,
        playerIdentifier
    }, function(affectedRows)
        if affectedRows and affectedRows > 0 then
            print("^2[STG Clothing] ✓ Skin saved successfully for " .. playerIdentifier .. " (affected rows: " .. affectedRows .. ")^7")
        else
            print("^1[STG Clothing] ✗ Failed to save skin for " .. playerIdentifier .. " (affected rows: " .. tostring(affectedRows) .. ")^7")

            -- چک کردن اینکه آیا player در جدول users وجود دارد
            MySQL.query('SELECT identifier FROM users WHERE identifier = ?', {
                playerIdentifier
            }, function(result)
                if result and #result > 0 then
                    print("^3[STG Clothing] Player exists in users table^7")
                else
                    print("^1[STG Clothing] ✗ Player NOT found in users table!^7")
                end
            end)
        end
    end)
end)


