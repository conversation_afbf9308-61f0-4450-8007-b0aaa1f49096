ESX = nil

TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)

-- Wait for ESX to be ready
Citizen.CreateThread(function()
    while ESX == nil do
        Citizen.Wait(0)
    end
    print("^2[STG Clothing] ESX loaded successfully^7")
end)

-- ESX Compatibility Functions
function GetPlayerCash(xPlayer)
    if not xPlayer then return 0 end

    if xPlayer.getMoney then
        return xPlayer.getMoney()
    elseif xPlayer.money then
        return xPlayer.money
    else
        return 0
    end
end

function GetPlayerBank(xPlayer)
    if not xPlayer then return 0 end

    if xPlayer.getAccount then
        local bankAccount = xPlayer.getAccount('bank')
        if bankAccount then
            return bankAccount.money or 0
        end
    end

    if xPlayer.accounts then
        for i=1, #xPlayer.accounts do
            if xPlayer.accounts[i].name == 'bank' then
                return xPlayer.accounts[i].money or 0
            end
        end
    end

    return 0
end

function RemovePlayerCash(xPlayer, amount)
    if not xPlayer or not amount or amount <= 0 then return false end

    local currentMoney = GetPlayerCash(xPlayer)
    if currentMoney < amount then return false end

    if xPlayer.removeMoney then
        local result = xPlayer.removeMoney(amount)
        return result ~= false
    elseif xPlayer.money then
        xPlayer.money = xPlayer.money - amount
        return true
    end

    return false
end

function RemovePlayerBank(xPlayer, amount)
    if not xPlayer or not amount or amount <= 0 then return false end

    local currentMoney = GetPlayerBank(xPlayer)
    if currentMoney < amount then return false end

    if xPlayer.removeAccountMoney then
        local result = xPlayer.removeAccountMoney('bank', amount)
        return result ~= false
    end
    if xPlayer.accounts then
        for i=1, #xPlayer.accounts do
            if xPlayer.accounts[i].name == 'bank' then
                if xPlayer.accounts[i].money >= amount then
                    xPlayer.accounts[i].money = xPlayer.accounts[i].money - amount
                    return true
                end
                break
            end
        end
    end

    return false
end

function AddPlayerCash(xPlayer, amount)
    if not xPlayer or not amount or amount <= 0 then return false end

    if xPlayer.addMoney then
        xPlayer.addMoney(amount)
        return true
    elseif xPlayer.money then
        xPlayer.money = xPlayer.money + amount
        return true
    end

    return false
end

function AddPlayerBank(xPlayer, amount)
    if not xPlayer or not amount or amount <= 0 then return false end

    if xPlayer.addAccountMoney then
        xPlayer.addAccountMoney('bank', amount)
        return true
    end
    if xPlayer.accounts then
        for i=1, #xPlayer.accounts do
            if xPlayer.accounts[i].name == 'bank' then
                xPlayer.accounts[i].money = xPlayer.accounts[i].money + amount
                return true
            end
        end
    end

    return false
end


ESX.RegisterServerCallback('stg_clothing:getWardrobe', function(source, cb)
    local xPlayer = ESX.GetPlayerFromId(source)
    MySQL.query("SELECT * FROM stg_clothing WHERE owner = ?", {
        xPlayer.identifier
    }, function(result)
        cb(result)
    end)
end)


ESX.RegisterServerCallback('stg_clothing:getOutfit', function(source, cb, id)
    local xPlayer = ESX.GetPlayerFromId(source)
    MySQL.query("SELECT * FROM stg_clothing WHERE id = ?", {
        id
    }, function(result)
        cb(result[1])
    end)
end)


ESX.RegisterServerCallback('stg_clothing:getMoney', function(source, cb, type, money)
    -- صبر کردن تا ESX آماده بشه
    while ESX == nil do
        Citizen.Wait(100)
    end

    -- تلاش برای گرفتن player از ESX کاستوم
    local xPlayer = ESX.GetPlayerFromId(source)

    if not xPlayer then
        -- تلاش مجدد با تاخیر بیشتر
        Citizen.Wait(1000)
        xPlayer = ESX.GetPlayerFromId(source)

        if not xPlayer then
            print("^1[STG Clothing] ✗ Could not get player from ESX in getMoney^7")
            cb(false)
            return
        end
    end

    if type == "cash" then
        local playerMoney = GetPlayerCash(xPlayer)

        if playerMoney >= money then
            local success = RemovePlayerCash(xPlayer, money)
            cb(success)
        else
            cb(false)
        end
    else
        local bankMoney = GetPlayerBank(xPlayer)

        if bankMoney >= money then
            local success = RemovePlayerBank(xPlayer, money)
            cb(success)
        else
            cb(false)
        end
    end
end)


RegisterNetEvent('stg_clothing:saveOutfit')
AddEventHandler('stg_clothing:saveOutfit', function(name, skin)
    local xPlayer = ESX.GetPlayerFromId(source)
    MySQL.insert("INSERT INTO stg_clothing (owner, skin, name) VALUES (?, ?, ?)", {
        xPlayer.identifier,
        json.encode(skin),
        name
    }, function(insertId)

    end)
end)


RegisterNetEvent('stg_clothing:deleteWardrobe')
AddEventHandler('stg_clothing:deleteWardrobe', function(id)
    local src = source
    MySQL.query("DELETE FROM `stg_clothing` WHERE `id` = ?", {
        id
    }, function(affectedRows)
        TriggerClientEvent('stg_clothing:refreshWardrobe', src)
    end)
end)




ESX.RegisterServerCallback('stg_clothing:getSkin', function(source, cb, method)
    local xPlayer = ESX.GetPlayerFromId(source)

    if not xPlayer then
        print("^1[STG Clothing] ✗ Could not get player from ESX in getSkin^7")
        cb(nil)
        return
    end

    MySQL.query('SELECT skin FROM users WHERE identifier = ?', {
        xPlayer.identifier
    }, function(result)
        if result and #result > 0 and result[1].skin then
            local skinData = {
                skin = result[1].skin
            }
            cb(skinData)
        else
            cb(nil)
        end
    end)
end)


RegisterServerEvent("stg_clothing:saveSkin")
AddEventHandler("stg_clothing:saveSkin", function(model, skin)
    local _source = source
    local xPlayer = ESX.GetPlayerFromId(_source)

    print("^3[STG Clothing] SaveSkin called for source: " .. tostring(_source) .. "^7")

    if model == nil or skin == nil then
        print("^1[STG Clothing] ✗ Invalid data - model or skin is nil^7")
        return
    end

    if not xPlayer then
        print("^1[STG Clothing] ✗ Could not get player from ESX^7")
        return
    end

    print("^2[STG Clothing] ✓ Got player: " .. xPlayer.identifier .. "^7")

    MySQL.update('UPDATE users SET skin = ? WHERE identifier = ?', {
        skin,
        xPlayer.identifier
    }, function(affectedRows)
        if affectedRows and affectedRows > 0 then
            print("^2[STG Clothing] ✓ Skin saved successfully for " .. xPlayer.identifier .. " (affected rows: " .. affectedRows .. ")^7")
        else
            print("^1[STG Clothing] ✗ Failed to save skin for " .. xPlayer.identifier .. " (affected rows: " .. tostring(affectedRows) .. ")^7")
        end
    end)
end)

function SaveSkinToDatabase(identifier, skin)
    print("^3[STG Clothing] Attempting to save skin for: " .. identifier .. "^7")

    MySQL.update('UPDATE users SET skin = ? WHERE identifier = ?', {
        skin,
        identifier
    }, function(affectedRows)
        if affectedRows and affectedRows > 0 then
            print("^2[STG Clothing] ✓ Skin saved successfully for " .. identifier .. " (affected rows: " .. affectedRows .. ")^7")
        else
            print("^1[STG Clothing] ✗ Failed to save skin for " .. identifier .. " (affected rows: " .. tostring(affectedRows) .. ")^7")

            -- چک کردن اینکه آیا player در جدول users وجود دارد
            MySQL.query('SELECT identifier FROM users WHERE identifier = ?', {
                identifier
            }, function(result)
                if result and #result > 0 then
                    print("^3[STG Clothing] Player exists in users table^7")
                else
                    print("^1[STG Clothing] ✗ Player NOT found in users table!^7")
                end
            end)
        end
    end)
end


