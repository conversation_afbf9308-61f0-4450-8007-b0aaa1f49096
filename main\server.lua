ESX = nil

TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)

-- callback گرفتن لیست لباس‌ها از stg_clothing
ESX.RegisterServerCallback('stg_clothing:getWardrobe', function(source, cb)
    local xPlayer = ESX.GetPlayerFromId(source)
    MySQL.query("SELECT * FROM stg_clothing WHERE owner = ?", {
        xPlayer.identifier
    }, function(result)
        cb(result)
    end)
end)

-- callback گرفتن یک لباس خاص از stg_clothing با id
ESX.RegisterServerCallback('stg_clothing:getOutfit', function(source, cb, id)
    local xPlayer = ESX.GetPlayerFromId(source)
    MySQL.query("SELECT * FROM stg_clothing WHERE id = ?", {
        id
    }, function(result)
        cb(result[1])
    end)
end)

-- callback بررسی و کم‌کردن پول برای خرید لباس
ESX.RegisterServerCallback('stg_clothing:getMoney', function(source, cb, type, money)
    local xPlayer = ESX.GetPlayerFromId(source)

    if not xPlayer then
        print("^1[STG Clothing] Error: Player not found for source " .. source)
        cb(false)
        return
    end

    if type == "cash" then
        -- بررسی پول نقد - سازگار با انواع مختلف ESX
        local playerMoney = 0
        if xPlayer.getMoney then
            playerMoney = xPlayer.getMoney()
        elseif xPlayer.money then
            playerMoney = xPlayer.money
        end

        if playerMoney >= money then
            if xPlayer.removeMoney then
                xPlayer.removeMoney(money)
            elseif xPlayer.money then
                xPlayer.money = xPlayer.money - money
            end
            cb(true)
        else
            cb(false)
        end
    else
        -- بررسی پول بانک
        local bankMoney = 0
        local bankAccount = nil

        if xPlayer.getAccount then
            bankAccount = xPlayer.getAccount('bank')
            if bankAccount then
                bankMoney = bankAccount.money
            end
        end

        if bankMoney >= money then
            if xPlayer.removeAccountMoney then
                xPlayer.removeAccountMoney('bank', money)
            end
            cb(true)
        else
            cb(false)
        end
    end
end)

-- ذخیره لباس جدید در stg_clothing
RegisterNetEvent('stg_clothing:saveOutfit')
AddEventHandler('stg_clothing:saveOutfit', function(name, skin)
    local xPlayer = ESX.GetPlayerFromId(source)
    MySQL.insert("INSERT INTO stg_clothing (owner, skin, name) VALUES (?, ?, ?)", {
        xPlayer.identifier,
        json.encode(skin),
        name
    }, function(insertId)
        -- اختیاری: می‌تونی اینجا پیامی برای کلاینت بفرستی
    end)
end)

-- حذف لباس از کمد
RegisterNetEvent('stg_clothing:deleteWardrobe')
AddEventHandler('stg_clothing:deleteWardrobe', function(id)
    local src = source
    MySQL.query("DELETE FROM `stg_clothing` WHERE `id` = ?", {
        id
    }, function(affectedRows)
        TriggerClientEvent('stg_clothing:refreshWardrobe', src)
    end)
end)

-- callback گرفتن اسکین فعال از جدول playerskins
ESX.RegisterServerCallback('stg_clothing:getSkin', function(source, cb, method)
    local xPlayer = ESX.GetPlayerFromId(source)

    if not xPlayer then
        print("^1[STG Clothing] Error: Player not found for source " .. source)
        cb(nil)
        return
    end

    MySQL.query('SELECT * FROM playerskins WHERE identifier = ? AND active = 1', {
        xPlayer.identifier
    }, function(result)
        if result and #result > 0 then
            cb(result[1])
        else
            print("^3[STG Clothing] Warning: No active skin found for player " .. xPlayer.identifier .. ", returning nil^7")
            cb(nil)
        end
    end)
end)

-- ذخیره اسکین جدید به عنوان active
RegisterServerEvent("stg_clothing:saveSkin")
AddEventHandler("stg_clothing:saveSkin", function(model, skin)
    local src = source
    local Player = ESX.GetPlayerFromId(src)
    if model ~= nil and skin ~= nil then
        MySQL.query('DELETE FROM playerskins WHERE identifier = ?', {
            Player.identifier
        }, function()
            MySQL.insert('INSERT INTO playerskins (identifier, model, skin, active) VALUES (?, ?, ?, 1)', {
                Player.identifier,
                model,
                skin
            })
        end)
    end
end)
