ESX = nil

TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)

-- ESX Compatibility Functions
function GetPlayerCash(xPlayer)
    if not xPlayer then return 0 end

    if xPlayer.getMoney then
        return xPlayer.getMoney()
    elseif xPlayer.money then
        return xPlayer.money
    else
        return 0
    end
end

function GetPlayerBank(xPlayer)
    if not xPlayer then return 0 end

    if xPlayer.getAccount then
        local bankAccount = xPlayer.getAccount('bank')
        if bankAccount then
            return bankAccount.money or 0
        end
    end

    if xPlayer.accounts then
        for i=1, #xPlayer.accounts do
            if xPlayer.accounts[i].name == 'bank' then
                return xPlayer.accounts[i].money or 0
            end
        end
    end

    return 0
end

function RemovePlayerCash(xPlayer, amount)
    if not xPlayer or not amount or amount <= 0 then return false end

    local currentMoney = GetPlayerCash(xPlayer)
    if currentMoney < amount then return false end

    if xPlayer.removeMoney then
        local result = xPlayer.removeMoney(amount)
        return result ~= false
    elseif xPlayer.money then
        xPlayer.money = xPlayer.money - amount
        return true
    end

    return false
end

function RemovePlayerBank(xPlayer, amount)
    if not xPlayer or not amount or amount <= 0 then return false end

    local currentMoney = GetPlayerBank(xPlayer)
    if currentMoney < amount then return false end

    if xPlayer.removeAccountMoney then
        local result = xPlayer.removeAccountMoney('bank', amount)
        return result ~= false
    end
    if xPlayer.accounts then
        for i=1, #xPlayer.accounts do
            if xPlayer.accounts[i].name == 'bank' then
                if xPlayer.accounts[i].money >= amount then
                    xPlayer.accounts[i].money = xPlayer.accounts[i].money - amount
                    return true
                end
                break
            end
        end
    end

    return false
end

function AddPlayerCash(xPlayer, amount)
    if not xPlayer or not amount or amount <= 0 then return false end

    if xPlayer.addMoney then
        xPlayer.addMoney(amount)
        return true
    elseif xPlayer.money then
        xPlayer.money = xPlayer.money + amount
        return true
    end

    return false
end

function AddPlayerBank(xPlayer, amount)
    if not xPlayer or not amount or amount <= 0 then return false end

    if xPlayer.addAccountMoney then
        xPlayer.addAccountMoney('bank', amount)
        return true
    end
    if xPlayer.accounts then
        for i=1, #xPlayer.accounts do
            if xPlayer.accounts[i].name == 'bank' then
                xPlayer.accounts[i].money = xPlayer.accounts[i].money + amount
                return true
            end
        end
    end

    return false
end


ESX.RegisterServerCallback('stg_clothing:getWardrobe', function(source, cb)
    local xPlayer = ESX.GetPlayerFromId(source)
    MySQL.query("SELECT * FROM stg_clothing WHERE owner = ?", {
        xPlayer.identifier
    }, function(result)
        cb(result)
    end)
end)


ESX.RegisterServerCallback('stg_clothing:getOutfit', function(source, cb, id)
    local xPlayer = ESX.GetPlayerFromId(source)
    MySQL.query("SELECT * FROM stg_clothing WHERE id = ?", {
        id
    }, function(result)
        cb(result[1])
    end)
end)


ESX.RegisterServerCallback('stg_clothing:getMoney', function(source, cb, type, money)
    local xPlayer = ESX.GetPlayerFromId(source)

    if not xPlayer then
        cb(false)
        return
    end

    if type == "cash" then
        local playerMoney = GetPlayerCash(xPlayer)

        if playerMoney >= money then
            local success = RemovePlayerCash(xPlayer, money)
            cb(success)
        else
            cb(false)
        end
    else
        local bankMoney = GetPlayerBank(xPlayer)

        if bankMoney >= money then
            local success = RemovePlayerBank(xPlayer, money)
            cb(success)
        else
            cb(false)
        end
    end
end)


RegisterNetEvent('stg_clothing:saveOutfit')
AddEventHandler('stg_clothing:saveOutfit', function(name, skin)
    local xPlayer = ESX.GetPlayerFromId(source)
    MySQL.insert("INSERT INTO stg_clothing (owner, skin, name) VALUES (?, ?, ?)", {
        xPlayer.identifier,
        json.encode(skin),
        name
    }, function(insertId)

    end)
end)


RegisterNetEvent('stg_clothing:deleteWardrobe')
AddEventHandler('stg_clothing:deleteWardrobe', function(id)
    local src = source
    MySQL.query("DELETE FROM `stg_clothing` WHERE `id` = ?", {
        id
    }, function(affectedRows)
        TriggerClientEvent('stg_clothing:refreshWardrobe', src)
    end)
end)




ESX.RegisterServerCallback('stg_clothing:getSkin', function(source, cb, method)
    local xPlayer = ESX.GetPlayerFromId(source)

    if not xPlayer then
        cb(nil)
        return
    end

    MySQL.query('SELECT skin FROM users WHERE identifier = ?', {
        xPlayer.identifier
    }, function(result)
        if result and #result > 0 and result[1].skin then
            local skinData = {
                skin = result[1].skin
            }
            cb(skinData)
        else
            cb(nil)
        end
    end)
end)


RegisterServerEvent("stg_clothing:saveSkin")
AddEventHandler("stg_clothing:saveSkin", function(model, skin)
    local src = source
    local Player = ESX.GetPlayerFromId(src)

    print("^3[STG Clothing] SaveSkin called for player: " .. (Player and Player.identifier or "unknown") .. "^7")
    print("^3[STG Clothing] Model: " .. tostring(model) .. "^7")
    print("^3[STG Clothing] Skin data length: " .. (skin and string.len(skin) or "nil") .. "^7")

    if model ~= nil and skin ~= nil and Player then
        MySQL.update('UPDATE users SET skin = ? WHERE identifier = ?', {
            skin,
            Player.identifier
        }, function(affectedRows)
            if affectedRows > 0 then
                print("^2[STG Clothing] ✓ Skin saved successfully for " .. Player.identifier .. "^7")
            else
                print("^1[STG Clothing] ✗ Failed to save skin for " .. Player.identifier .. "^7")
            end
        end)
    else
        print("^1[STG Clothing] ✗ Invalid data - model: " .. tostring(model) .. ", skin: " .. tostring(skin ~= nil) .. ", player: " .. tostring(Player ~= nil) .. "^7")
    end
end)


