ESX = nil

TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)

-- callback گرفتن لیست لباس‌ها از stg_clothing
ESX.RegisterServerCallback('stg_clothing:getWardrobe', function(source, cb)
    local xPlayer = ESX.GetPlayerFromId(source)
    MySQL.query("SELECT * FROM stg_clothing WHERE owner = ?", {
        xPlayer.identifier
    }, function(result)
        cb(result)
    end)
end)

-- callback گرفتن یک لباس خاص از stg_clothing با id
ESX.RegisterServerCallback('stg_clothing:getOutfit', function(source, cb, id)
    local xPlayer = ESX.GetPlayerFromId(source)
    MySQL.query("SELECT * FROM stg_clothing WHERE id = ?", {
        id
    }, function(result)
        cb(result[1])
    end)
end)

-- callback بررسی و کم‌کردن پول برای خرید لباس
ESX.RegisterServerCallback('stg_clothing:getMoney', function(source, cb, type, money)
    local xPlayer = ESX.GetPlayerFromId(source)

    if not xPlayer then
        print("^1[STG Clothing] Error: Player not found for source " .. source)
        cb(false)
        return
    end

    if type == "cash" then
        -- بررسی پول نقد - سازگار با انواع مختلف ESX
        local playerMoney = 0

        if xPlayer.getMoney then
            playerMoney = xPlayer.getMoney()
        elseif xPlayer.money then
            playerMoney = xPlayer.money
        else
            print("^1[STG Clothing] Error: Cannot get player money^7")
            cb(false)
            return
        end

        print("^3[STG Clothing] Debug: Player cash: " .. playerMoney .. ", Required: " .. money .. "^7")

        if playerMoney >= money then
            local success = false

            if xPlayer.removeMoney then
                success = xPlayer.removeMoney(money)
                if success == nil then success = true end -- برخی ESX ها boolean برنمیگردونن
            elseif xPlayer.money then
                xPlayer.money = xPlayer.money - money
                success = true
            end

            if success then
                print("^2[STG Clothing] Successfully removed " .. money .. " cash^7")
                cb(true)
            else
                print("^1[STG Clothing] Failed to remove cash^7")
                cb(false)
            end
        else
            print("^1[STG Clothing] Insufficient cash^7")
            cb(false)
        end
    else
        -- بررسی پول بانک
        local bankMoney = 0
        local bankAccount = nil

        if xPlayer.getAccount then
            bankAccount = xPlayer.getAccount('bank')
            if bankAccount then
                bankMoney = bankAccount.money
            end
        end

        if bankMoney >= money then
            if xPlayer.removeAccountMoney then
                xPlayer.removeAccountMoney('bank', money)
            end
            cb(true)
        else
            cb(false)
        end
    end
end)

-- ذخیره لباس جدید در stg_clothing
RegisterNetEvent('stg_clothing:saveOutfit')
AddEventHandler('stg_clothing:saveOutfit', function(name, skin)
    local xPlayer = ESX.GetPlayerFromId(source)
    MySQL.insert("INSERT INTO stg_clothing (owner, skin, name) VALUES (?, ?, ?)", {
        xPlayer.identifier,
        json.encode(skin),
        name
    }, function(insertId)
        -- اختیاری: می‌تونی اینجا پیامی برای کلاینت بفرستی
    end)
end)

-- حذف لباس از کمد
RegisterNetEvent('stg_clothing:deleteWardrobe')
AddEventHandler('stg_clothing:deleteWardrobe', function(id)
    local src = source
    MySQL.query("DELETE FROM `stg_clothing` WHERE `id` = ?", {
        id
    }, function(affectedRows)
        TriggerClientEvent('stg_clothing:refreshWardrobe', src)
    end)
end)

-- event برای چک کردن پول بازیکن (برای دیباگ)
RegisterNetEvent('stg_clothing:checkMoney')
AddEventHandler('stg_clothing:checkMoney', function()
    local src = source
    local xPlayer = ESX.GetPlayerFromId(src)

    if not xPlayer then
        print("^1[STG Clothing] Error: Player not found^7")
        return
    end

    -- چک پول نقد
    local cash = 0
    if xPlayer.getMoney then
        cash = xPlayer.getMoney()
    elseif xPlayer.money then
        cash = xPlayer.money
    end

    -- چک پول بانک
    local bank = 0
    if xPlayer.getAccount then
        local bankAccount = xPlayer.getAccount('bank')
        if bankAccount then
            bank = bankAccount.money or 0
        end
    elseif xPlayer.accounts then
        for i=1, #xPlayer.accounts do
            if xPlayer.accounts[i].name == 'bank' then
                bank = xPlayer.accounts[i].money or 0
                break
            end
        end
    end

    print("^2[STG Clothing] Player " .. xPlayer.identifier .. " money:^7")
    print("^2  Cash: $" .. cash .. "^7")
    print("^2  Bank: $" .. bank .. "^7")

    TriggerClientEvent('chat:addMessage', src, {
        color = {0, 255, 0},
        multiline = true,
        args = {"STG Money", "Cash: $" .. cash .. " | Bank: $" .. bank}
    })
end)

-- event برای اضافه کردن پول (برای تست)
RegisterNetEvent('stg_clothing:addMoney')
AddEventHandler('stg_clothing:addMoney', function(moneyType, amount)
    local src = source
    local xPlayer = ESX.GetPlayerFromId(src)

    if not xPlayer then
        print("^1[STG Clothing] Error: Player not found^7")
        return
    end

    if moneyType == "cash" then
        if xPlayer.addMoney then
            xPlayer.addMoney(amount)
        elseif xPlayer.money then
            xPlayer.money = xPlayer.money + amount
        end
        print("^2[STG Clothing] Added $" .. amount .. " cash to player^7")
    elseif moneyType == "bank" then
        if xPlayer.addAccountMoney then
            xPlayer.addAccountMoney('bank', amount)
        elseif xPlayer.accounts then
            for i=1, #xPlayer.accounts do
                if xPlayer.accounts[i].name == 'bank' then
                    xPlayer.accounts[i].money = xPlayer.accounts[i].money + amount
                    break
                end
            end
        end
        print("^2[STG Clothing] Added $" .. amount .. " to bank^7")
    end

    TriggerClientEvent('chat:addMessage', src, {
        color = {0, 255, 0},
        multiline = true,
        args = {"STG Money", "Added $" .. amount .. " " .. moneyType}
    })
end)

-- callback گرفتن اسکین فعال از جدول playerskins
ESX.RegisterServerCallback('stg_clothing:getSkin', function(source, cb, method)
    local xPlayer = ESX.GetPlayerFromId(source)

    if not xPlayer then
        print("^1[STG Clothing] Error: Player not found for source " .. source)
        cb(nil)
        return
    end

    MySQL.query('SELECT * FROM playerskins WHERE identifier = ? AND active = 1', {
        xPlayer.identifier
    }, function(result)
        if result and #result > 0 then
            cb(result[1])
        else
            print("^3[STG Clothing] Warning: No active skin found for player " .. xPlayer.identifier .. ", returning nil^7")
            cb(nil)
        end
    end)
end)

-- ذخیره اسکین جدید به عنوان active
RegisterServerEvent("stg_clothing:saveSkin")
AddEventHandler("stg_clothing:saveSkin", function(model, skin)
    local src = source
    local Player = ESX.GetPlayerFromId(src)
    if model ~= nil and skin ~= nil then
        MySQL.query('DELETE FROM playerskins WHERE identifier = ?', {
            Player.identifier
        }, function()
            MySQL.insert('INSERT INTO playerskins (identifier, model, skin, active) VALUES (?, ?, ?, 1)', {
                Player.identifier,
                model,
                skin
            })
        end)
    end
end)
